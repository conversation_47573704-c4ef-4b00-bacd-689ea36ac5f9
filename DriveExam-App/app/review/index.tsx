import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { <PERSON><PERSON>, <PERSON>adingSpinner } from '../../src/components/common';
import { WrongQuestionsService } from '../../src/services/questions/wrongQuestionsService';
import { useWrongQuestionsStore } from '../../src/store/useWrongQuestionsStore';
import { ProcessedQuestion } from '../../src/types/question';
import { COLORS } from '../../src/utils/constants';

export default function WrongQuestionsReviewScreen() {
  const [loading, setLoading] = useState(true);
  const [wrongQuestionsCount, setWrongQuestionsCount] = useState(0);
  const [wrongQuestions, setWrongQuestions] = useState<ProcessedQuestion[]>([]);

  const { startReviewSession } = useWrongQuestionsStore();

  useEffect(() => {
    loadWrongQuestions();
  }, []);

  const loadWrongQuestions = async () => {
    try {
      setLoading(true);

      const [questions, count] = await Promise.all([
        WrongQuestionsService.getWrongQuestions(),
        WrongQuestionsService.getWrongQuestionsCount()
      ]);

      setWrongQuestions(questions);
      setWrongQuestionsCount(count);
    } catch (error) {
      console.error('Failed to load wrong questions:', error);
      Alert.alert('載入失敗', '無法載入錯題集，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const handleStartReview = () => {
    if (wrongQuestions.length === 0) {
      Alert.alert('暫無錯題', '您目前沒有錯題需要複習！');
      return;
    }

    startReviewSession(wrongQuestions);
    router.replace('/review/session' as any);
  };

  const getVolumeDistribution = () => {
    const volumeCount = new Map<number, number>();

    wrongQuestions.forEach(question => {
      const count = volumeCount.get(question.volume) || 0;
      volumeCount.set(question.volume, count + 1);
    });

    return Array.from(volumeCount.entries()).sort((a, b) => a[0] - b[0]);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner message="正在載入錯題集..." />
      </SafeAreaView>
    );
  }

  const volumeDistribution = getVolumeDistribution();

  return (
    <View style={styles.container}>
      <StatusBar style="auto" backgroundColor={COLORS.BACKGROUND} />

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        {/* <View style={styles.header}>
          <Text style={styles.title}>錯題回顧</Text>
          <Text style={styles.subtitle}>複習您曾經答錯的題目，加強薄弱環節</Text>
        </View> */}

        {/* Description */}
        {wrongQuestionsCount > 0 && (
          <View style={styles.descriptionCard}>
            <Text style={styles.descriptionTitle}>複習說明</Text>
            <Text style={styles.descriptionText}>
              • 複習您曾經答錯的題目，加強薄弱環節
            </Text>
            <Text style={styles.descriptionText}>
              • 複習過程中答對的題目將從錯題集中移除
            </Text>
            <Text style={styles.descriptionText}>
              • 答錯的題目將保留在錯題集中
            </Text>
          </View>
        )}

        {/* Statistics Card */}
        <View style={styles.statsCard}>
          <View style={styles.statsHeader}>
            <Text style={styles.statsTitle}>錯題統計</Text>
            <View style={styles.totalBadge}>
              <Text style={styles.totalCount}>{wrongQuestionsCount}</Text>
            </View>
          </View>

          {wrongQuestionsCount > 0 ? (
            <View style={styles.volumeStats}>
              {volumeDistribution.map(([volume, count]) => (
                <View key={volume} style={styles.volumeRow}>
                  <Text style={styles.volumeLabel}>第{volume}冊</Text>
                  <Text style={styles.volumeCount}>{count}題</Text>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>🎉</Text>
              <Text style={styles.emptyTitle}>太棒了！</Text>
              <Text style={styles.emptySubtitle}>您目前沒有錯題需要複習</Text>
            </View>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <Button
            title={wrongQuestionsCount > 0 ? "開始複習錯題" : "返回主頁"}
            onPress={wrongQuestionsCount > 0 ? handleStartReview : () => router.back()}
            style={wrongQuestionsCount > 0 ? styles.startButton : styles.backButton}
            textStyle={wrongQuestionsCount > 0 ? styles.startButtonText : styles.backButtonText}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    paddingVertical: 24,
    paddingHorizontal: 20,
  },
  header: {
    marginBottom: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.SECONDARY_TEXT,
    textAlign: 'center',
    lineHeight: 22,
  },
  statsCard: {
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: COLORS.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  totalBadge: {
    backgroundColor: COLORS.ACCENT,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  totalCount: {
    color: COLORS.CARD_BACKGROUND,
    fontSize: 16,
    fontWeight: '700',
  },
  volumeStats: {
    gap: 12,
  },
  volumeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  volumeLabel: {
    fontSize: 16,
    color: COLORS.TEXT,
    fontWeight: '500',
  },
  volumeCount: {
    fontSize: 16,
    color: COLORS.SECONDARY_TEXT,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 40,
    marginBottom: 12,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: COLORS.SECONDARY_TEXT,
    textAlign: 'center',
  },
  descriptionCard: {
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: COLORS.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.TEXT,
    marginBottom: 8,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: 15,
    color: COLORS.TEXT,
    lineHeight: 22,
    marginBottom: 4,
  },
  actionContainer: {
    gap: 12,
  },
  startButton: {
    backgroundColor: COLORS.ACCENT,
    borderRadius: 16,
    paddingVertical: 18,
    paddingHorizontal: 24,
    shadowColor: COLORS.ACCENT,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
  },
  startButtonText: {
    color: COLORS.CARD_BACKGROUND,
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
  },
  backButton: {
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderColor: COLORS.BORDER,
    borderWidth: 1,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  backButtonText: {
    color: COLORS.TEXT,
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
  },
}); 