# 錯題回顧模組 (Wrong Questions Review Module)

## 概述 (Overview)

錯題回顧模組是澳門考車神器 App 的核心功能之一，旨在幫助用戶針對性地複習曾經答錯的題目，提高學習效率和考試通過率。

## 功能特點 (Key Features)

### 1. 智能錯題收集
- **自動收集**: 在練習模式中答錯的題目自動進入錯題集
- **實時更新**: 每次答題後立即更新錯題狀態
- **按冊分類**: 錯題按照題庫冊數（第1-5冊）自動分類

### 2. 動態錯題管理
- **智能移除**: 複習時答對的題目自動從錯題集中移除
- **保留機制**: 複習時答錯的題目繼續保留在錯題集中
- **進度追蹤**: 實時顯示剩餘錯題數量和複習進度

### 3. 用戶友好界面
- **統計展示**: 清晰展示各冊錯題數量分布
- **進度可視化**: 實時顯示答題正確率和進度條
- **操作提示**: 詳細的使用說明和操作指導

## 技術架構 (Technical Architecture)

### 1. 服務層 (Services)
```typescript
// src/services/questions/wrongQuestionsService.ts
export class WrongQuestionsService {
  // 獲取錯題列表
  static async getWrongQuestions(): Promise<ProcessedQuestion[]>
  
  // 從錯題集中移除題目
  static async removeFromWrongQuestions(questionId: number): Promise<void>
  
  // 獲取錯題總數
  static async getWrongQuestionsCount(): Promise<number>
}
```

### 2. 狀態管理 (State Management)
```typescript
// src/store/useWrongQuestionsStore.ts
interface WrongQuestionsReviewState {
  isActive: boolean
  questions: ProcessedQuestion[]
  currentQuestion: ProcessedQuestion | null
  stats: SessionStats
  // ... 其他狀態
}
```

### 3. 頁面結構 (Page Structure)
```
app/review/
├── index.tsx          # 錯題回顧首頁
└── session.tsx        # 錯題複習會話頁面
```

## 數據流程 (Data Flow)

### 1. 錯題收集流程
```mermaid
graph LR
    A[練習答題] --> B{答案正確?}
    B -->|否| C[記錄到 question_stats 表]
    C --> D[wrong_attempts += 1]
    B -->|是| E[不記錄為錯題]
```

### 2. 錯題複習流程
```mermaid
graph LR
    A[進入錯題回顧] --> B[載入錯題列表]
    B --> C[開始複習會話]
    C --> D[答題]
    D --> E{答案正確?}
    E -->|是| F[從錯題集移除]
    E -->|否| G[保留在錯題集]
    F --> H[更新統計]
    G --> H
    H --> I{還有錯題?}
    I -->|是| C
    I -->|否| J[完成複習]
```

## 數據庫設計 (Database Schema)

### 相關數據表
錯題回顧功能主要依賴現有的數據庫表結構：

#### 1. question_stats 表
```sql
CREATE TABLE question_stats (
    question_id INTEGER PRIMARY KEY,
    volume INTEGER NOT NULL,
    total_attempts INTEGER DEFAULT 0,
    correct_attempts INTEGER DEFAULT 0,
    wrong_attempts INTEGER DEFAULT 0,    -- 錯題判斷依據
    last_attempted DATETIME,
    is_bookmarked BOOLEAN DEFAULT 0,
    note TEXT
);
```

#### 2. answer_records 表
```sql
CREATE TABLE answer_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_id INTEGER NOT NULL,
    is_correct BOOLEAN NOT NULL,         -- 用於更新錯題統計
    mode TEXT NOT NULL,                  -- 'practice' 或 'review'
    -- ... 其他欄位
);
```

### 錯題查詢邏輯
```sql
-- 獲取所有錯題
SELECT * FROM question_stats 
WHERE wrong_attempts > 0 
ORDER BY last_attempted DESC;

-- 獲取特定冊數的錯題
SELECT * FROM question_stats 
WHERE volume = ? AND wrong_attempts > 0 
ORDER BY last_attempted DESC;
```

## 用戶界面 (User Interface)

### 1. 錯題回顧首頁 (`/review`)
- **功能**: 顯示錯題統計、按冊分布、開始複習
- **組件**: 統計卡片、操作按鈕、使用說明
- **狀態**: 載入中、有錯題、無錯題

### 2. 錯題複習會話 (`/review/session`)
- **功能**: 進行錯題複習、實時統計、進度追蹤
- **組件**: 題目卡片、選項列表、答案反饋、導航按鈕
- **特色**: 答對題目可選擇移除、剩餘題目實時顯示

## API 接口 (API Interface)

### 1. 錯題服務接口
```typescript
interface WrongQuestionsAPI {
  // 獲取錯題列表
  getWrongQuestions(): Promise<ProcessedQuestion[]>
  
  // 獲取錯題數量
  getWrongQuestionsCount(): Promise<number>
  
  // 移除錯題
  removeFromWrongQuestions(questionId: number): Promise<void>
}
```

### 2. 複習會話接口
```typescript
interface ReviewSessionAPI {
  // 開始複習會話
  startReviewSession(questions: ProcessedQuestion[]): void
  
  // 提交答案
  submitAnswer(): void
  
  // 移除當前題目
  removeCurrentQuestion(): void
  
  // 結束會話
  endSession(): void
}
```

## 性能優化 (Performance Optimization)

### 1. 數據加載優化
- **分批加載**: 按冊分別加載錯題，避免一次性加載大量數據
- **緩存機制**: 利用 QuestionLoader 的緩存機制減少重複加載
- **索引優化**: question_stats 表上建立適當索引提高查詢性能

### 2. 用戶體驗優化
- **即時反饋**: 答題後立即顯示結果和移除選項
- **進度顯示**: 實時更新剩餘題目數量和正確率
- **流暢導航**: 支援上一題/下一題無縫切換

## 錯誤處理 (Error Handling)

### 1. 數據載入錯誤
```typescript
try {
  const wrongQuestions = await WrongQuestionsService.getWrongQuestions();
} catch (error) {
  Alert.alert('載入失敗', '無法載入錯題集，請稍後再試。');
}
```

### 2. 答案記錄錯誤
```typescript
try {
  await recordAnswer(answerData);
  if (isCorrect) {
    await WrongQuestionsService.removeFromWrongQuestions(questionId);
  }
} catch (error) {
  console.error('Failed to record answer:', error);
  // 不向用戶顯示錯誤，僅記錄日誌
}
```

## 測試策略 (Testing Strategy)

### 1. 單元測試
- 錯題服務功能測試
- 狀態管理邏輯測試
- 數據庫操作測試

### 2. 集成測試
- 錯題收集流程測試
- 複習會話完整流程測試
- 數據同步測試

### 3. 用戶體驗測試
- 界面響應性測試
- 複習流程用戶體驗測試
- 邊界情況處理測試

## 部署說明 (Deployment Notes)

### 1. 數據庫遷移
由於使用現有的數據庫表結構，無需額外的數據庫遷移。

### 2. 新增路由
確保 Expo Router 正確識別新增的路由：
- `/review` -> `app/review/index.tsx`
- `/review/session` -> `app/review/session.tsx`

### 3. 依賴檢查
確認所有必要的依賴項已正確安裝：
- React Navigation 相關組件
- 狀態管理 (Zustand)
- 數據庫服務 (SQLite)

## 未來擴展 (Future Enhancements)

### 1. 高級功能
- **錯題難度分析**: 根據錯誤次數標記題目難度
- **錯題分類複習**: 支援按章節或標籤進行錯題複習
- **錯題筆記功能**: 允許用戶為錯題添加個人筆記

### 2. 學習分析
- **錯題趨勢分析**: 分析用戶錯題變化趨勢
- **薄弱知識點識別**: 智能識別用戶薄弱的知識領域
- **複習建議**: 基於錯題數據提供個性化複習建議

### 3. 社交功能
- **錯題分享**: 允許用戶分享常見錯題
- **群體錯題統計**: 顯示群體常錯題目排行
- **錯題討論區**: 針對錯題進行討論和解答

## 總結 (Conclusion)

錯題回顧模組是一個完整的、用戶友好的學習輔助功能，通過智能的錯題收集和管理機制，幫助用戶有效提高學習效率。該模組採用現代化的技術架構，具有良好的可擴展性和維護性，為用戶提供優質的學習體驗。 