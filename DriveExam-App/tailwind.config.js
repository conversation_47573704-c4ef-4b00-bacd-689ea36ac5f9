/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom colors from your app
        'app-theme': 'rgba(163, 196, 201, 1)',
        'app-theme-text': 'rgb(68, 169, 184)',
        'app-theme-disabled': 'rgb(163, 163, 163)',
        'app-primary': 'rgb(177, 197, 218)',
        'app-success': 'rgba(52, 199, 89, 1)',
        'app-error': 'rgba(255, 59, 48, 1)',
        'app-warning': 'rgba(255, 149, 0, 1)',
        'app-background': 'rgb(241, 237, 229)',
        'app-card': 'rgba(255, 255, 255, 1)',
        'app-card-disabled': 'rgba(140, 140, 140, 0.1)',
        'app-accent': 'rgba(163, 196, 201, 1)',
        'app-accent-dark': 'rgba(123, 165, 171, 1)',
        'app-text': 'rgba(44, 44, 46, 1)',
        'app-text-light': 'rgba(109, 109, 112, 1)',
        'app-text-secondary': 'rgba(142, 142, 147, 1)',
        'app-border': 'rgba(229, 229, 234, 1)',
        'app-shadow': 'rgba(0, 0, 0, 0.2)',
      },
    },
  },
  plugins: [],
}

