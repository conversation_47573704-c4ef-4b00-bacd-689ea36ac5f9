// src/utils/color.ts
export const setAlpha = (color: string, alpha: number): string => {
    const a = Math.max(0, Math.min(1, alpha));
    // rgba(...)
    if (color.startsWith('rgba')) {
      const [r, g, b] = color.match(/\d+(\.\d+)?/g)!.slice(0, 3);
      return `rgba(${r}, ${g}, ${b}, ${a})`;
    }
    // rgb(...)
    if (color.startsWith('rgb')) {
      const [r, g, b] = color.match(/\d+(\.\d+)?/g)!.slice(0, 3);
      return `rgba(${r}, ${g}, ${b}, ${a})`;
    }
    // #RRGGBB / #RGB / #RRGGBBAA
    if (color.startsWith('#')) {
      let hex = color.slice(1);
      if (hex.length === 3) hex = hex.split('').map(c => c + c).join('');
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, ${a})`;
    }
    return color;
};