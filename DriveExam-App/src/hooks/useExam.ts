import { useCallback, useEffect, useState } from 'react';
import { getExamHistory, getExamSessionDetails } from '../services/database/queries';
import { useExamStore } from '../store/useExamStore';
import { ExamResult, ExamSession } from '../types/session';

export interface UseExamReturn {
  // Current exam state
  isExamActive: boolean;
  currentSession: ExamSession | null;
  examResult: ExamResult | null;
  
  // Exam history
  examHistory: any[];
  loadingHistory: boolean;
  historyError: string | null;
  
  // Actions
  loadExamHistory: () => Promise<void>;
  getExamDetails: (sessionId: string) => Promise<any>;
  resetExam: () => void;
}

export function useExam(): UseExamReturn {
  const [examHistory, setExamHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [historyError, setHistoryError] = useState<string | null>(null);

  const { session, isActive, examResult, reset } = useExamStore();

  const loadExamHistory = useCallback(async () => {
    try {
      setLoadingHistory(true);
      setHistoryError(null);
      
      // Get exam history from database
      const history = await getExamHistory();
      setExamHistory(history || []);
    } catch (error) {
      console.error('Failed to load exam history:', error);
      setHistoryError(error instanceof Error ? error.message : '載入考試記錄失敗');
    } finally {
      setLoadingHistory(false);
    }
  }, []);

  const getExamDetails = useCallback(async (sessionId: string) => {
    try {
      const details = await getExamSessionDetails(sessionId);
      return details;
    } catch (error) {
      console.error('Failed to load exam details:', error);
      throw error;
    }
  }, []);

  const resetExam = useCallback(() => {
    reset();
  }, [reset]);

  // Auto-load exam history on mount
  useEffect(() => {
    loadExamHistory();
  }, [loadExamHistory]);

  return {
    // Current exam state
    isExamActive: isActive,
    currentSession: session,
    examResult,
    
    // Exam history
    examHistory,
    loadingHistory,
    historyError,
    
    // Actions
    loadExamHistory,
    getExamDetails,
    resetExam,
  };
}

export default useExam; 