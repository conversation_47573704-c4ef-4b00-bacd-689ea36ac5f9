import { getDatabase } from './init';
import { Session } from '../../types/database';

export interface CreateSessionData {
  id: string;
  type: 'practice' | 'exam';
  title: string;
  totalQuestions: number;
  volumes: number[];
  config: Record<string, any>;
}

export async function createSession(data: CreateSessionData): Promise<void> {
  const db = getDatabase();
  
  try {
    await db.runAsync(
      `INSERT INTO sessions 
       (id, type, title, total_questions, volumes, config) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        data.id,
        data.type,
        data.title,
        data.totalQuestions,
        JSON.stringify(data.volumes),
        JSON.stringify(data.config),
      ]
    );
  } catch (error) {
    console.error('Failed to create session:', error);
    throw error;
  }
}

export async function updateSessionProgress(
  sessionId: string,
  correctCount: number,
  wrongCount: number
): Promise<void> {
  const db = getDatabase();
  
  try {
    await db.runAsync(
      `UPDATE sessions 
       SET correct_count = ?, wrong_count = ? 
       WHERE id = ?`,
      [correctCount, wrongCount, sessionId]
    );
  } catch (error) {
    console.error('Failed to update session progress:', error);
    throw error;
  }
}

export async function completeSession(
  sessionId: string,
  durationSeconds: number
): Promise<void> {
  const db = getDatabase();
  
  try {
    await db.runAsync(
      `UPDATE sessions 
       SET is_completed = 1, 
           duration_seconds = ?, 
           completed_at = datetime('now') 
       WHERE id = ?`,
      [durationSeconds, sessionId]
    );
  } catch (error) {
    console.error('Failed to complete session:', error);
    throw error;
  }
}

export async function getSession(sessionId: string): Promise<Session | null> {
  const db = getDatabase();
  
  try {
    const result = await db.getFirstAsync(
      `SELECT * FROM sessions WHERE id = ?`,
      [sessionId]
    ) as Session | null;
    
    return result;
  } catch (error) {
    console.error('Failed to get session:', error);
    throw error;
  }
}

export async function getRecentSessions(
  type?: 'practice' | 'exam',
  limit: number = 10
): Promise<Session[]> {
  const db = getDatabase();
  
  try {
    const query = type
      ? `SELECT * FROM sessions WHERE type = ? ORDER BY created_at DESC LIMIT ?`
      : `SELECT * FROM sessions ORDER BY created_at DESC LIMIT ?`;
    
    const params = type ? [type, limit] : [limit];
    
    const result = await db.getAllAsync(query, params) as Session[];
    
    return result;
  } catch (error) {
    console.error('Failed to get recent sessions:', error);
    throw error;
  }
}