/**
 * Progress Tracking Service
 * Handles volume progress updates and statistics calculation
 * Ensures accurate progress tracking across all volumes
 */

import { getDatabase } from './init';
import { VOLUMES } from '../../utils/constants';

export interface VolumeProgressData {
  volume: number;
  totalQuestions: number;
  seenCount: number;
  correctCount: number;
  wrongCount: number;
  lastPractice?: string;
  updatedAt: string;
}

export class ProgressService {
  private static instance: ProgressService;

  static getInstance(): ProgressService {
    if (!ProgressService.instance) {
      ProgressService.instance = new ProgressService();
    }
    return ProgressService.instance;
  }

  private getDB() {
    return getDatabase();
  }

  /**
   * Initialize volume progress for all volumes
   * Should be called on app startup
   */
  async initializeVolumeProgress(): Promise<void> {
    try {
      for (let volume = 1; volume <= 5; volume++) {
        const totalQuestions = VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS] || 0;
        
        await this.getDB().runAsync(
          `INSERT OR IGNORE INTO volume_progress (
            volume, total_questions, seen_count, correct_count, wrong_count, updated_at
          ) VALUES (?, ?, 0, 0, 0, datetime('now'))`,
          [volume, totalQuestions]
        );
      }
    } catch (error) {
      console.error('Failed to initialize volume progress:', error);
      throw error;
    }
  }

  /**
   * Update volume progress after practice session
   * Recalculates all statistics from answer_records for accuracy
   */
  async updateVolumeProgress(volume: number): Promise<void> {
    try {
      const db = this.getDB();
      
      // Calculate current progress from answer_records
      const progressData = await db.getFirstAsync(
        `SELECT 
           COUNT(DISTINCT question_id) as seen_count,
           SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
           SUM(CASE WHEN is_correct = 0 THEN 1 ELSE 0 END) as wrong_count,
           MAX(created_at) as last_practice
         FROM answer_records 
         WHERE volume = ?`,
        [volume]
      ) as {
        seen_count: number;
        correct_count: number;
        wrong_count: number;
        last_practice: string;
      };

      // Update volume progress
      await db.runAsync(
        `UPDATE volume_progress 
         SET seen_count = ?, 
             correct_count = ?, 
             wrong_count = ?,
             last_practice = ?,
             updated_at = datetime('now')
         WHERE volume = ?`,
        [
          progressData.seen_count || 0,
          progressData.correct_count || 0,
          progressData.wrong_count || 0,
          progressData.last_practice,
          volume
        ]
      );
    } catch (error) {
      console.error('Failed to update volume progress:', error);
      throw error;
    }
  }

  /**
   * Update progress for multiple volumes
   */
  async updateMultipleVolumes(volumes: number[]): Promise<void> {
    try {
      for (const volume of volumes) {
        await this.updateVolumeProgress(volume);
      }
    } catch (error) {
      console.error('Failed to update multiple volumes progress:', error);
      throw error;
    }
  }

  /**
   * Get volume progress data
   */
  async getVolumeProgress(volume: number): Promise<VolumeProgressData | null> {
    try {
      const result = await this.getDB().getFirstAsync(
        `SELECT * FROM volume_progress WHERE volume = ?`,
        [volume]
      ) as any;

      if (!result) return null;

      return {
        volume: result.volume,
        totalQuestions: result.total_questions,
        seenCount: result.seen_count,
        correctCount: result.correct_count,
        wrongCount: result.wrong_count,
        lastPractice: result.last_practice,
        updatedAt: result.updated_at
      };
    } catch (error) {
      console.error('Failed to get volume progress:', error);
      throw error;
    }
  }

  /**
   * Get all volumes progress
   */
  async getAllVolumesProgress(): Promise<VolumeProgressData[]> {
    try {
      const results = await this.getDB().getAllAsync(
        `SELECT * FROM volume_progress ORDER BY volume ASC`
      ) as any[];

      return results.map(result => ({
        volume: result.volume,
        totalQuestions: result.total_questions,
        seenCount: result.seen_count,
        correctCount: result.correct_count,
        wrongCount: result.wrong_count,
        lastPractice: result.last_practice,
        updatedAt: result.updated_at
      }));
    } catch (error) {
      console.error('Failed to get all volumes progress:', error);
      throw error;
    }
  }

  /**
   * Recalculate all volume progress from answer_records
   * Useful for data integrity checks and migrations
   */
  async recalculateAllProgress(): Promise<void> {
    try {
      for (let volume = 1; volume <= 5; volume++) {
        await this.updateVolumeProgress(volume);
      }
    } catch (error) {
      console.error('Failed to recalculate all progress:', error);
      throw error;
    }
  }

  /**
   * Get volume progress summary
   */
  async getProgressSummary(): Promise<{
    totalQuestions: number;
    totalSeen: number;
    totalCorrect: number;
    totalWrong: number;
    overallAccuracy: number;
    volumeCompletionRates: { volume: number; completionRate: number }[];
  }> {
    try {
      const allProgress = await this.getAllVolumesProgress();
      
      const summary = allProgress.reduce((acc, progress) => {
        acc.totalQuestions += progress.totalQuestions;
        acc.totalSeen += progress.seenCount;
        acc.totalCorrect += progress.correctCount;
        acc.totalWrong += progress.wrongCount;
        return acc;
      }, {
        totalQuestions: 0,
        totalSeen: 0,
        totalCorrect: 0,
        totalWrong: 0
      });

      const overallAccuracy = (summary.totalCorrect + summary.totalWrong) > 0
        ? (summary.totalCorrect / (summary.totalCorrect + summary.totalWrong)) * 100
        : 0;

      const volumeCompletionRates = allProgress.map(progress => ({
        volume: progress.volume,
        completionRate: progress.totalQuestions > 0
          ? (progress.seenCount / progress.totalQuestions) * 100
          : 0
      }));

      return {
        ...summary,
        overallAccuracy,
        volumeCompletionRates
      };
    } catch (error) {
      console.error('Failed to get progress summary:', error);
      throw error;
    }
  }
}

export const progressService = ProgressService.getInstance();