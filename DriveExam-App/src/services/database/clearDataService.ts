import { Alert } from 'react-native';
import { resetDatabase } from './init';

/**
 * Clear all application data by resetting the SQLite database
 * This will remove all user progress, statistics, and stored data
 */
export async function clearAllData(): Promise<void> {
  return new Promise((resolve, reject) => {
    Alert.alert(
      '清除所有數據',
      '這將永久刪除所有學習進度、統計數據和答題記錄。此操作無法撤銷。\n\n您確定要繼續嗎？',
      [
        {
          text: '取消',
          style: 'cancel',
          onPress: () => resolve(),
        },
        {
          text: '確認清除',
          style: 'destructive',
          onPress: async () => {
            try {
              await resetDatabase();
              Alert.alert(
                '清除完成',
                '所有數據已成功清除',
                [{ text: '確定', onPress: () => resolve() }]
              );
            } catch (error) {
              console.error('Failed to clear data:', error);
              Alert.alert(
                '清除失敗',
                '清除數據時發生錯誤，請稍後重試',
                [{ text: '確定', onPress: () => reject(error) }]
              );
            }
          },
        },
      ]
    );
  });
}