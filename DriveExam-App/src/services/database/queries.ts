import { QuestionStats, VolumeProgress } from '../../types/database';
import { getDatabase } from './init';

export async function getVolumeProgress(): Promise<VolumeProgress[]> {
  const db = getDatabase();

  try {
    const result = await db.getAllAsync(
      `SELECT * FROM volume_progress ORDER BY volume ASC`
    ) as VolumeProgress[];

    return result;
  } catch (error) {
    console.error('Failed to get volume progress:', error);
    throw error;
  }
}

export async function getSeenQuestionIds(volumes: number[]): Promise<Set<number>> {
  const { dataService } = await import('./dataService');
  return dataService.getSeenQuestionIds(volumes);
}

export async function getQuestionStats(questionId: number, volume: number): Promise<QuestionStats | null> {
  const db = getDatabase();
  
  try {
    // Try composite primary key approach first
    let result = await db.getFirstAsync(
      `SELECT * FROM question_stats WHERE question_id = ? AND volume = ?`,
      [questionId, volume]
    ) as QuestionStats | null;
    
    // If not found, try single key approach (for backward compatibility)
    if (!result) {
      result = await db.getFirstAsync(
        `SELECT * FROM question_stats WHERE question_id = ?`,
        [questionId]
      ) as QuestionStats | null;
    }
    
    return result;
  } catch (error) {
    console.error('Failed to get question stats:', error);
    throw error;
  }
}

export async function getWrongQuestions(volume?: number): Promise<QuestionStats[]> {
  const db = getDatabase();
  
  try {
    const query = volume
      ? `SELECT * FROM question_stats 
         WHERE volume = ? AND wrong_attempts > 0 
         ORDER BY last_attempted DESC`
      : `SELECT * FROM question_stats 
         WHERE wrong_attempts > 0 
         ORDER BY last_attempted DESC`;
    
    const params = volume ? [volume] : [];
    
    const result = await db.getAllAsync(query, params) as QuestionStats[];
    
    return result;
  } catch (error) {
    console.error('Failed to get wrong questions:', error);
    throw error;
  }
}

export async function getBookmarkedQuestions(volume?: number): Promise<QuestionStats[]> {
  const db = getDatabase();
  
  try {
    const query = volume
      ? `SELECT * FROM question_stats 
         WHERE volume = ? AND is_bookmarked = 1 
         ORDER BY last_attempted DESC`
      : `SELECT * FROM question_stats 
         WHERE is_bookmarked = 1 
         ORDER BY last_attempted DESC`;
    
    const params = volume ? [volume] : [];
    
    const result = await db.getAllAsync(query, params) as QuestionStats[];
    
    return result;
  } catch (error) {
    console.error('Failed to get bookmarked questions:', error);
    throw error;
  }
}

export async function toggleBookmark(questionId: number, volume: number): Promise<void> {
  const db = getDatabase();
  
  try {
    // Try composite primary key approach first
    const result = await db.runAsync(
      `UPDATE question_stats 
       SET is_bookmarked = CASE 
         WHEN is_bookmarked = 1 THEN 0 
         ELSE 1 
       END 
       WHERE question_id = ? AND volume = ?`,
      [questionId, volume]
    );
    
    // If no rows affected, try single key approach
    if (result.changes === 0) {
      await db.runAsync(
        `UPDATE question_stats 
         SET is_bookmarked = CASE 
           WHEN is_bookmarked = 1 THEN 0 
           ELSE 1 
         END,
         volume = ?
         WHERE question_id = ?`,
        [volume, questionId]
      );
    }
  } catch (error) {
    console.error('Failed to toggle bookmark:', error);
    throw error;
  }
}

export async function updateQuestionNote(
  questionId: number,
  volume: number,
  note: string
): Promise<void> {
  const db = getDatabase();
  
  try {
    // Try composite primary key approach first
    await db.runAsync(
      `INSERT INTO question_stats (question_id, volume, note)
       VALUES (?, ?, ?)
       ON CONFLICT(question_id, volume) DO UPDATE SET
       note = ?`,
      [questionId, volume, note, note]
    );
  } catch (error) {
    // If that fails, try single key approach
    console.log('Composite key note update failed, trying single key approach:', error);
    
    try {
      await db.runAsync(
        `INSERT INTO question_stats (question_id, volume, note)
         VALUES (?, ?, ?)
         ON CONFLICT(question_id) DO UPDATE SET
         volume = ?, note = ?`,
        [questionId, volume, note, volume, note]
      );
    } catch (fallbackError) {
      console.error('Failed to update question note:', fallbackError);
      throw fallbackError;
    }
  }
}

export async function getWrongQuestionIds(volumes: number[]): Promise<Set<number>> {
  const { dataService } = await import('./dataService');
  return dataService.getWrongQuestionIds(volumes);
}

export async function getBookmarkedQuestionIds(volumes: number[]): Promise<Set<number>> {
  const { dataService } = await import('./dataService');
  return dataService.getBookmarkedQuestionIds(volumes);
}

// Exam-related queries
export async function getExamHistory(): Promise<any[]> {
  const db = getDatabase();

  try {
    const result = await db.getAllAsync(
      `SELECT * FROM sessions 
       WHERE type = 'exam' 
       ORDER BY created_at DESC`
    );

    return result || [];
  } catch (error) {
    console.error('Failed to get exam history:', error);
    throw error;
  }
}

export async function getExamSessionDetails(sessionId: string): Promise<any> {
  const db = getDatabase();

  try {
    const session = await db.getFirstAsync(
      `SELECT * FROM sessions WHERE id = ?`,
      [sessionId]
    );

    const answers = await db.getAllAsync(
      `SELECT * FROM answer_records 
       WHERE session_id = ? AND mode = 'exam' 
       ORDER BY id ASC`,
      [sessionId]
    );

    return {
      session,
      answers: answers || [],
    };
  } catch (error) {
    console.error('Failed to get exam session details:', error);
    throw error;
  }
}