export const CREATE_TABLES = `
  -- 1. 答題記錄表（每題實時寫入）
  CREATE TABLE IF NOT EXISTS answer_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_id INTEGER NOT NULL,
    volume INTEGER NOT NULL,           -- 冊數 (1-5)
    chapter INTEGER NOT NULL,          -- 章節
    is_correct BOOLEAN NOT NULL,
    mode TEXT NOT NULL,                -- 'practice' | 'exam'
    session_id TEXT NOT NULL,          -- 會話ID (UUID)
    selected_option TEXT,              -- 用戶選擇 (A/B/C/D)
    correct_option TEXT,               -- 正確答案 (A/B/C/D)
    time_spent INTEGER,                -- 答題用時（秒）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  -- 2. 練習/考試會話表
  CREATE TABLE IF NOT EXISTS sessions (
    id TEXT PRIMARY KEY,               -- UUID
    type TEXT NOT NULL,                -- 'practice' | 'exam'
    title TEXT,                        -- 會話標題（如：第1冊練習）
    total_questions INTEGER,
    correct_count INTEGER DEFAULT 0,
    wrong_count INTEGER DEFAULT 0,
    duration_seconds INTEGER,
    volumes TEXT,                      -- JSON array [1,2,3]
    config TEXT,                       -- JSON 練習設定或考試規則
    is_completed BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
  );

  -- 3. 用戶進度表（按冊統計）
  CREATE TABLE IF NOT EXISTS volume_progress (
    volume INTEGER PRIMARY KEY,
    total_questions INTEGER,           -- 該冊總題數
    seen_count INTEGER DEFAULT 0,      -- 已看過的題目數
    correct_count INTEGER DEFAULT 0,
    wrong_count INTEGER DEFAULT 0,
    last_practice DATETIME,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  -- 4. 題目統計表（追蹤每道題的表現）
  CREATE TABLE IF NOT EXISTS question_stats (
    question_id INTEGER NOT NULL,
    volume INTEGER NOT NULL,
    total_attempts INTEGER DEFAULT 0,
    correct_attempts INTEGER DEFAULT 0,
    wrong_attempts INTEGER DEFAULT 0,
    last_attempted DATETIME,
    is_bookmarked BOOLEAN DEFAULT 0,
    note TEXT,                          -- 用戶筆記
    PRIMARY KEY (question_id, volume)
  );
`;

export const CREATE_INDEXES = `
  -- 索引優化查詢性能
  CREATE INDEX IF NOT EXISTS idx_records_session ON answer_records(session_id);
  CREATE INDEX IF NOT EXISTS idx_records_question ON answer_records(question_id);
  CREATE INDEX IF NOT EXISTS idx_records_created ON answer_records(created_at);
  CREATE INDEX IF NOT EXISTS idx_sessions_type ON sessions(type);
  CREATE INDEX IF NOT EXISTS idx_sessions_created ON sessions(created_at DESC);
  CREATE INDEX IF NOT EXISTS idx_question_stats_volume ON question_stats(volume);
`;