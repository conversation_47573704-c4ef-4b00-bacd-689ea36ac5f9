import { OverallStats, PredictedPassRate, VolumeStats } from '../../types/statistics';
import { VOLUMES } from '../../utils/constants';
import { getDatabase } from '../database/init';

export class StatisticsCalculator {
  private static instance: StatisticsCalculator;

  static getInstance(): StatisticsCalculator {
    if (!StatisticsCalculator.instance) {
      StatisticsCalculator.instance = new StatisticsCalculator();
    }
    return StatisticsCalculator.instance;
  }

  private getDB() {
    return getDatabase();
  }

  /**
   * Calculate statistics for a specific volume
   */
  async calculateVolumeStats(volume: number): Promise<VolumeStats> {

    // Get total questions for this volume from the question files
    const totalQuestions = VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS] || 0;

    // Calculate question-level statistics from answer_records
    const questionStats = await this.getDB().getAllAsync(
      `SELECT 
        question_id,
        SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
        SUM(CASE WHEN is_correct = 0 THEN 1 ELSE 0 END) as wrong_count,
        COUNT(*) as total_attempts
       FROM answer_records 
       WHERE volume = ?
       GROUP BY question_id`,
      [volume]
    ) as any[];

    const seenQuestions = questionStats.length;
    const masteredQuestions = questionStats.filter(q => q.correct_count > 0).length; // Any correct answer = mastered
    const wrongQuestions = questionStats.filter(q => q.correct_count === 0 && q.wrong_count > 0).length; // Only wrong if never correct
    
    // Calculate total attempts for accuracy
    const totalCorrectAttempts = questionStats.reduce((sum, q) => sum + q.correct_count, 0);
    const totalWrongAttempts = questionStats.reduce((sum, q) => sum + q.wrong_count, 0);
    const accuracy = (totalCorrectAttempts + totalWrongAttempts) > 0 ? 
      (totalCorrectAttempts / (totalCorrectAttempts + totalWrongAttempts)) * 100 : 0;

    return {
      volume,
      totalQuestions,
      seenQuestions,
      correctAnswers: masteredQuestions, // Count of questions mastered (not attempts)
      wrongAnswers: wrongQuestions, // Count of questions still wrong (not attempts)
      accuracy: isNaN(accuracy) ? 0 : accuracy,
    };
  }

  /**
   * Calculate overall statistics across all volumes
   */
  async calculateOverallStats(): Promise<OverallStats> {
    // Calculate volume stats for all volumes (1-5)
    const volumeStats: VolumeStats[] = [];
    for (let volume = 1; volume <= 5; volume++) {
      const stats = await this.calculateVolumeStats(volume);
      volumeStats.push(stats);
    }

    // Calculate overall statistics
    const totalQuestionsAnswered = volumeStats.reduce((sum, vol) => sum + vol.correctAnswers + vol.wrongAnswers, 0);
    const totalCorrect = volumeStats.reduce((sum, vol) => sum + vol.correctAnswers, 0);
    const totalWrong = volumeStats.reduce((sum, vol) => sum + vol.wrongAnswers, 0);
    const overallAccuracy = totalQuestionsAnswered > 0 ? (totalCorrect / totalQuestionsAnswered) * 100 : 0;

    // Calculate streak (consecutive correct answers in recent sessions)
    const streakResult = await this.getDB().getFirstAsync(
      `WITH consecutive_correct AS (
        SELECT 
          ROW_NUMBER() OVER (ORDER BY created_at DESC) - 
          ROW_NUMBER() OVER (PARTITION BY is_correct ORDER BY created_at DESC) as grp
        FROM answer_records 
        WHERE is_correct = 1
        ORDER BY created_at DESC
        LIMIT 50
      )
      SELECT COUNT(*) as streak 
      FROM consecutive_correct 
      WHERE grp = 0`
    ) as any;

    // Calculate average time per question
    const timeResult = await this.getDB().getFirstAsync(
      `SELECT AVG(time_spent) as avg_time, SUM(time_spent) as total_time
       FROM answer_records 
       WHERE time_spent > 0`
    ) as any;

    return {
      totalQuestionsAnswered,
      correctAnswers: totalCorrect,
      wrongAnswers: totalWrong,
      accuracy: isNaN(overallAccuracy) ? 0 : overallAccuracy,
      streakCount: streakResult?.streak || 0,
      totalTimeSpent: timeResult?.total_time || 0,
      averageTimePerQuestion: timeResult?.avg_time || 45,
      volumeStats,
    };
  }

  /**
   * Calculate predicted pass rate based on current performance
   */
  async calculatePredictedPassRate(): Promise<PredictedPassRate> {
    const overallStats = await this.calculateOverallStats();
    
    // Get recent performance (last 100 answers)
    const recentPerformance = await this.getDB().getAllAsync(
      `SELECT is_correct, volume 
       FROM answer_records 
       ORDER BY created_at DESC 
       LIMIT 100`
    ) as any[];

    if (recentPerformance.length === 0) {
      return {
        percentage: 0,
        confidence: 'low',
        recommendations: [
          '開始練習以獲得預測結果',
          '建議完成至少50道題目',
        ],
      };
    }

    // Calculate recent accuracy
    const recentCorrect = recentPerformance.filter(r => r.is_correct).length;
    const recentAccuracy = (recentCorrect / recentPerformance.length) * 100;

    // Calculate volume coverage for available volumes (1-5)
    const availableVolumeStats = overallStats.volumeStats.filter(vol => vol.volume >= 1);
    const volumeCoverage = availableVolumeStats.map(vol => {
      const coverage = vol.totalQuestions > 0 ? vol.seenQuestions / vol.totalQuestions : 0;
      return coverage;
    });
    const avgCoverage = volumeCoverage.length > 0 ? volumeCoverage.reduce((sum, cov) => sum + cov, 0) / volumeCoverage.length : 0;

    // Calculate predicted pass rate using weighted formula
    let predictedRate = 0;
    let confidence: 'low' | 'medium' | 'high' = 'low';

    if (overallStats.totalQuestionsAnswered >= 200 && avgCoverage >= 0.5) {
      // High confidence prediction
      predictedRate = Math.min(95, recentAccuracy * 0.7 + overallStats.accuracy * 0.3);
      confidence = 'high';
    } else if (overallStats.totalQuestionsAnswered >= 50) {
      // Medium confidence prediction
      predictedRate = Math.min(90, recentAccuracy * 0.8 + overallStats.accuracy * 0.2);
      confidence = 'medium';
    } else {
      // Low confidence prediction
      predictedRate = Math.min(85, recentAccuracy * 0.9);
      confidence = 'low';
    }

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (predictedRate < 70) {
      recommendations.push('建議加強基礎練習，目標正確率達到80%以上');
    }
    
    if (avgCoverage < 0.6) {
      recommendations.push('建議練習更多不同冊別的題目');
    }

    // Check weak volumes across 1-5
    const weakVolumes = overallStats.volumeStats
      .filter(vol => vol.volume >= 1 && vol.accuracy < 70 && vol.seenQuestions > 10)
      .map(vol => vol.volume);
    
    if (weakVolumes.length > 0) {
      recommendations.push(`重點加強第${weakVolumes.join('、')}冊的練習`);
    }

    if (overallStats.totalQuestionsAnswered < 300) {
      recommendations.push(`建議完成至少300道題目後再考試（目前已完成${overallStats.totalQuestionsAnswered}道）`);
    }

    if (recommendations.length === 0) {
      recommendations.push('您的準備情況良好，可以考慮參加考試！');
    }

    return {
      percentage: Math.round(predictedRate),
      confidence,
      recommendations,
    };
  }

  /**
   * Get volume display stats with additional info for dashboard cards
   */
  async getVolumeDisplayStats(): Promise<{
    volume: number;
    title: string;
    total: number;
    correct: number;
    wrong: number;
    unseen: number;
  }[]> {
    const displayStats = [];
    
    // Add volumes 1-5 with actual stats
    for (let volume = 1; volume <= 5; volume++) {
      const stats = await this.calculateVolumeStats(volume);
      displayStats.push({
        volume: stats.volume,
        title: VOLUMES.NAMES[stats.volume as keyof typeof VOLUMES.NAMES] || `第${stats.volume}冊`,
        total: stats.totalQuestions,
        correct: stats.correctAnswers,
        wrong: stats.wrongAnswers,
        unseen: stats.totalQuestions - stats.seenQuestions,
      });
    }

    return displayStats;
  }

  /**
   * Update volume progress after practice session
   */
  async updateVolumeProgress(volume: number, questionsAnswered: number, correctAnswers: number): Promise<void> {
    await this.getDB().runAsync(
      `INSERT OR REPLACE INTO volume_progress 
       (volume, total_questions, seen_count, correct_count, wrong_count, last_practice, updated_at)
       VALUES (?, ?, 
         COALESCE((SELECT seen_count FROM volume_progress WHERE volume = ?), 0) + ?,
         COALESCE((SELECT correct_count FROM volume_progress WHERE volume = ?), 0) + ?,
         COALESCE((SELECT wrong_count FROM volume_progress WHERE volume = ?), 0) + ?,
         datetime('now'), datetime('now'))`,
      [
        volume,
        VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS] || 0,
        volume, questionsAnswered,
        volume, correctAnswers,
        volume, questionsAnswered - correctAnswers
      ]
    );
  }
}

export const statisticsCalculator = StatisticsCalculator.getInstance();