import shuffle from 'lodash.shuffle';
import { ProcessedQuestion } from '../../types/question';
import { ExamPaperMap, PracticeConfig } from '../../types/session';
import { EXAM_RULES } from '../../utils/constants';
import { createShuffledOptions } from '../../utils/questionUtils';
import { QuestionLoader } from './loader';

export class QuestionManager {
  static async generatePracticeQuestions(config: PracticeConfig): Promise<ProcessedQuestion[]> {
    try {
      let questions: ProcessedQuestion[] = [];

      // Load questions from selected volumes
      for (const volume of config.volumes) {
        const volumeQuestions = await QuestionLoader.loadVolume(volume);
        questions.push(...volumeQuestions);
      }

      // TODO: Filter by chapter if needed in the future
      // Chapter filtering not implemented yet as questions don't have chapter property

      // Handle question filtering - combine wrong and unseen questions
      if (config.includeWrongQuestions || config.includeUnseen) {
        try {
          let filteredQuestions: ProcessedQuestion[] = [];
          const questionIds = new Set<number>();
          
          // Include wrong questions if requested
          if (config.includeWrongQuestions) {
            const { getWrongQuestionIds } = await import('../database/queries');
            const wrongQuestionIds = await getWrongQuestionIds(config.volumes);
            wrongQuestionIds.forEach(id => questionIds.add(id));
            console.log(`Found ${wrongQuestionIds.size} wrong questions for practice`);
          }
          
          // Include unseen questions if requested
          if (config.includeUnseen) {
            const { getSeenQuestionIds } = await import('../database/queries');
            const seenQuestionIds = await getSeenQuestionIds(config.volumes);
            questions.forEach(q => {
              if (!seenQuestionIds.has(q.id)) {
                questionIds.add(q.id);
              }
            });
            const unseenCount = questions.filter(q => !seenQuestionIds.has(q.id)).length;
            console.log(`Found ${unseenCount} unseen questions for practice`);
          }
          
          // Filter questions based on collected IDs
          if (questionIds.size > 0) {
            filteredQuestions = questions.filter(q => questionIds.has(q.id));
            questions = filteredQuestions;
            console.log(`Total practice questions: ${filteredQuestions.length}`);
          } else {
            console.log('No filtered questions available, using all questions for review');
            // Keep all questions for review
          }
        } catch (error) {
          console.warn('Failed to filter questions, showing all questions:', error);
          // Continue with all questions if filtering fails
        }
      }


      // Include bookmarked questions if specified
      if (config.includeBookmarked) {
        try {
          const { getBookmarkedQuestionIds } = await import('../database/queries');
          const bookmarkedQuestionIds = await getBookmarkedQuestionIds(config.volumes);
          questions = questions.filter(q => bookmarkedQuestionIds.has(q.id));
        } catch (error) {
          console.warn('Failed to load bookmarked questions:', error);
        }
      }

      // Shuffle if random mode
      if (config.mode === 'random') {
        questions = shuffle(questions);
      }

      // For continuous practice mode, return all available questions
      // Users can practice indefinitely until they choose to leave
      return questions;
    } catch (error) {
      console.error('Failed to generate practice questions:', error);
      throw error;
    }
  }

  static async generateExamQuestions(): Promise<{ questions: ProcessedQuestion[]; paperMap: ExamPaperMap }> {
    try {
      // Load questions from each volume without pre-shuffling options
      // We'll handle option shuffling with mapping preservation
      const volume1Questions = await QuestionLoader.loadVolumeWithoutShuffle(1);
      const volume2Questions = await QuestionLoader.loadVolumeWithoutShuffle(2);
      const volume3Questions = await QuestionLoader.loadVolumeWithoutShuffle(3);
      const volume4Questions = await QuestionLoader.loadVolumeWithoutShuffle(4);
      const volume5Questions = await QuestionLoader.loadVolumeWithoutShuffle(5);

      // Select questions for each set
      const set1Questions = shuffle(volume1Questions).slice(0, EXAM_RULES.QUESTIONS_PER_VOLUME[1]);
      const set2Questions = shuffle(volume2Questions).slice(0, EXAM_RULES.QUESTIONS_PER_VOLUME[2]);
      const set3Questions = shuffle(volume3Questions).slice(0, EXAM_RULES.QUESTIONS_PER_VOLUME[3]);
      const set4Questions = shuffle(volume4Questions).slice(0, EXAM_RULES.QUESTIONS_PER_VOLUME[4]);
      const set5Questions = shuffle(volume5Questions).slice(0, EXAM_RULES.QUESTIONS_PER_VOLUME[5]);

      // Combine all questions in set order (before final shuffle)
      const orderedQuestions = [
        ...set1Questions,
        ...set2Questions,
        ...set3Questions,
        ...set4Questions,
        ...set5Questions
      ];

      // Create array with set information for mapping
      const questionsWithSetInfo = orderedQuestions.map((question, index) => {
        let setNumber: number;
        let setIndex: number;

        if (index < EXAM_RULES.QUESTIONS_PER_VOLUME[1]) {
          setNumber = 1;
          setIndex = index;
        } else if (index < EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2]) {
          setNumber = 2;
          setIndex = index - EXAM_RULES.QUESTIONS_PER_VOLUME[1];
        } else if (index < EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3]) {
          setNumber = 3;
          setIndex = index - EXAM_RULES.QUESTIONS_PER_VOLUME[1] - EXAM_RULES.QUESTIONS_PER_VOLUME[2];
        } else if (index < EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3] + EXAM_RULES.QUESTIONS_PER_VOLUME[4]) {
          setNumber = 4;
          setIndex = index - EXAM_RULES.QUESTIONS_PER_VOLUME[1] - EXAM_RULES.QUESTIONS_PER_VOLUME[2] - EXAM_RULES.QUESTIONS_PER_VOLUME[3];
        } else {
          setNumber = 5;
          setIndex = index - EXAM_RULES.QUESTIONS_PER_VOLUME[1] - EXAM_RULES.QUESTIONS_PER_VOLUME[2] - EXAM_RULES.QUESTIONS_PER_VOLUME[3] - EXAM_RULES.QUESTIONS_PER_VOLUME[4];
        }

        return {
          question,
          originalIndex: index,
          setNumber,
          setIndex
        };
      });

      // Shuffle the questions for the final exam order
      const shuffledQuestionsWithInfo = shuffle(questionsWithSetInfo);

      // Process questions with option shuffling and create mappings
      const examQuestions: ProcessedQuestion[] = [];
      const paperMap: ExamPaperMap = {
        questionMap: {},
        optionMaps: {},
        setBoundaries: {
          set1: { start: 0, end: EXAM_RULES.QUESTIONS_PER_VOLUME[1] - 1, count: EXAM_RULES.QUESTIONS_PER_VOLUME[1] },
          set2: { start: EXAM_RULES.QUESTIONS_PER_VOLUME[1], end: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] - 1, count: EXAM_RULES.QUESTIONS_PER_VOLUME[2] },
          set3: { start: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2], end: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3] - 1, count: EXAM_RULES.QUESTIONS_PER_VOLUME[3] },
          set4: { start: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3], end: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3] + EXAM_RULES.QUESTIONS_PER_VOLUME[4] - 1, count: EXAM_RULES.QUESTIONS_PER_VOLUME[4] },
          set5: { start: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3] + EXAM_RULES.QUESTIONS_PER_VOLUME[4], end: EXAM_RULES.QUESTIONS_PER_VOLUME[1] + EXAM_RULES.QUESTIONS_PER_VOLUME[2] + EXAM_RULES.QUESTIONS_PER_VOLUME[3] + EXAM_RULES.QUESTIONS_PER_VOLUME[4] + EXAM_RULES.QUESTIONS_PER_VOLUME[5] - 1, count: EXAM_RULES.QUESTIONS_PER_VOLUME[5] }
        }
      };

      shuffledQuestionsWithInfo.forEach((item, examIndex) => {
        const { question, setNumber, setIndex } = item;

        // Create shuffled options with mapping
        const optionShuffleResult = createShuffledOptions(question.options);

        // Create the processed question with shuffled options
        const processedQuestion: ProcessedQuestion = {
          ...question,
          options: optionShuffleResult.shuffledOptions
        };

        examQuestions.push(processedQuestion);

        // Store question mapping
        paperMap.questionMap[examIndex] = {
          originalQuestionId: question.id,
          originalVolume: question.volume,
          setNumber,
          setIndex
        };

        // Store option mapping
        paperMap.optionMaps[examIndex] = {
          displayToOriginal: optionShuffleResult.shuffledOptions.map((_, displayIndex) =>
            optionShuffleResult.mapShuffledToOriginal(displayIndex)
          ),
          originalToDisplay: question.options.map((_, originalIndex) =>
            optionShuffleResult.mapOriginalToShuffled(originalIndex)
          )
        };
      });

      return { questions: examQuestions, paperMap };
    } catch (error) {
      console.error('Failed to generate exam questions:', error);
      throw error;
    }
  }

  static validateQuestionSet(questions: ProcessedQuestion[]): boolean {
    if (questions.length === 0) {
      return false;
    }

    // Check each question has required fields
    return questions.every(q => 
      q.id &&
      q.volume &&
      q.question &&
      q.options &&
      Array.isArray(q.options) &&
      q.options.length === 4 &&
      q.options.some(opt => opt.isCorrect) // At least one correct answer
    );
  }

  static getQuestionsByVolume(questions: ProcessedQuestion[]): Record<number, ProcessedQuestion[]> {
    const grouped: Record<number, ProcessedQuestion[]> = {};
    
    questions.forEach(question => {
      if (!grouped[question.volume]) {
        grouped[question.volume] = [];
      }
      grouped[question.volume].push(question);
    });
    
    return grouped;
  }

  static getQuestionsByTags(questions: ProcessedQuestion[], volume?: number): Record<string, ProcessedQuestion[]> {
    let filteredQuestions = questions;
    
    if (volume) {
      filteredQuestions = questions.filter(q => q.volume === volume);
    }

    const grouped: Record<string, ProcessedQuestion[]> = {};
    
    filteredQuestions.forEach(question => {
      const tags = question.tags || ['未分類'];
      tags.forEach(tag => {
        const key = `${question.volume}-${tag}`;
        if (!grouped[key]) {
          grouped[key] = [];
        }
        grouped[key].push(question);
      });
    });
    
    return grouped;
  }
}