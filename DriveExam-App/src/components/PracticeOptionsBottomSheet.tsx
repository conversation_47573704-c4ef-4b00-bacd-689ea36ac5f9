import { BottomSheetModal, BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import { router } from 'expo-router';
import React, { useMemo, useState } from 'react';
import { Alert, Text, View } from 'react-native';

import { useStatistics } from '../hooks/useStatistics';
import { QuestionManager } from '../services/questions/manager';
import { usePracticeStore } from '../store/usePracticeStore';
import { PracticeConfig } from '../types/session';
import { COLORS, VOLUMES } from '../utils/constants';
import { <PERSON><PERSON>, Card } from './common';

interface PracticeOptionsBottomSheetProps {
  bottomSheetModalRef: React.RefObject<BottomSheetModal>;
}

export function PracticeOptionsBottomSheet({ bottomSheetModalRef }: PracticeOptionsBottomSheetProps) {
  const [loading, setLoading] = useState(false);
  const [selectedVolumes, setSelectedVolumes] = useState<number[]>([]);
  const [includeUnse<PERSON>, setIncludeUnseen] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [practiceMode, setPracticeMode] = useState<'sequential' | 'random'>('sequential');
  const { startSession } = usePracticeStore();
  const { volumeDisplayStats } = useStatistics();

  // Variables
  const snapPoints = useMemo(() => ['70%', '95%'], []);

  const startPractice = async (config: PracticeConfig) => {
    setLoading(true);

    try {
      const questions = await QuestionManager.generatePracticeQuestions(config);

      if (questions.length === 0) {
        Alert.alert('沒有題目', '所選範圍內沒有找到題目，請選擇其他選項。');
        return;
      }

      startSession(config, questions);
      bottomSheetModalRef.current?.dismiss();
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const toggleVolumeSelection = (volume: number) => {
    setSelectedVolumes(prev => {
      if (prev.includes(volume)) {
        return prev.filter(v => v !== volume);
      } else {
        return [...prev, volume].sort();
      }
    });
  };

  const handleStartPractice = () => {
    if (selectedVolumes.length === 0) {
      Alert.alert('請選擇冊別', '請至少選擇一個冊別進行練習。');
      return;
    }

    const config: PracticeConfig = {
      volumes: selectedVolumes,
      chapter: null,
      mode: practiceMode,
      includeWrongQuestions: false,
      includeBookmarked: false,
      includeUnseen,
    };

    startPractice(config);
  };

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={1}
      snapPoints={snapPoints}
      onChange={() => {}}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: COLORS.BACKGROUND }}
      handleIndicatorStyle={{ backgroundColor: COLORS.BORDER }}
    >
      <BottomSheetView style={styles.contentContainer}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>選擇練習模式</Text>
          <Button
            title="✕"
            onPress={() => bottomSheetModalRef.current?.dismiss()}
            variant="secondary"
            style={styles.closeButton}
            textStyle={styles.closeButtonText}
          />
        </View>

        {/* Scrollable Content */}
        <BottomSheetScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          style={styles.scrollView}
        >
          {/* Volume Selection */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>選擇練習冊別</Text>
              <Text style={styles.sectionSubtitle}>可選擇一個或多個冊別進行練習</Text>
            </View>

            <View style={styles.volumeGrid}>
              {Array.from({ length: VOLUMES.TOTAL }, (_, i) => i + 1).map(volume => {
                const isSelected = selectedVolumes.includes(volume);
                const volumeStats = volumeDisplayStats?.find(v => v.volume === volume);
                const accuracy = volumeStats ?
                  (volumeStats.correct / Math.max(1, volumeStats.correct + volumeStats.wrong) * 100) : 0;

                return (
                  <Card
                    key={volume}
                    style={isSelected ? [styles.volumeCard, styles.selectedVolumeCard] as any : styles.volumeCard}
                    onPress={() => toggleVolumeSelection(volume)}
                  >
                    <View style={styles.volumeHeader}>
                      <Text style={[
                        styles.volumeTitle,
                        isSelected && styles.selectedVolumeTitle
                      ]}>
                        第 {volume} 冊
                      </Text>
                      <View style={[
                        styles.checkbox,
                        isSelected && styles.checkedBox
                      ]}>
                        {isSelected && <Text style={styles.checkmark}>✓</Text>}
                      </View>
                    </View>
                    <Text style={[
                      styles.volumeSubtitle,
                      isSelected && styles.selectedVolumeSubtitle
                    ]}>
                      {volumeStats?.title || VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
                    </Text>

                    {volumeStats && (volumeStats.correct + volumeStats.wrong > 0) ? (
                      <View style={styles.statsContainer}>
                        <Text style={[
                          styles.accuracyText,
                          isSelected && styles.selectedVolumeDescription,
                          { color: accuracy >= 80 ? COLORS.SUCCESS : accuracy >= 60 ? COLORS.WARNING : COLORS.ERROR }
                        ]}>
                          正確率: {accuracy.toFixed(0)}%
                        </Text>
                        <Text style={[
                          styles.volumeDescription,
                          isSelected && styles.selectedVolumeDescription
                        ]}>
                          已練習 {volumeStats.correct + volumeStats.wrong} / {volumeStats.total} 題
                        </Text>
                      </View>
                    ) : (
                      <Text style={[
                        styles.volumeDescription,
                        isSelected && styles.selectedVolumeDescription
                      ]}>
                        共 {volumeStats?.total || VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS]} 道題目 · 未開始練習
                      </Text>
                    )}
                  </Card>
                );
              })}
            </View>
          </View>

          {/* Practice Options */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>練習選項</Text>

            <Card style={styles.optionCard}>
              <View style={styles.optionRow}>
                <Text style={styles.optionLabel}>題目範圍</Text>
                <View style={styles.optionButtons}>
                  <Button
                    title="只練習未做過的"
                    onPress={() => setIncludeUnseen(true)}
                    variant={includeUnseen ? 'primary' : 'secondary'}
                    size="small"
                    style={styles.optionButton}
                  />
                  <Button
                    title="全部題目"
                    onPress={() => setIncludeUnseen(false)}
                    variant={!includeUnseen ? 'primary' : 'secondary'}
                    size="small"
                    style={styles.optionButton}
                  />
                </View>
              </View>
            </Card>

            {/* Advanced Settings */}
            <Card style={styles.optionCard}>
              <View style={styles.optionRow}>
                <Text style={styles.optionLabel}>進階設定</Text>
                <Button
                  title={showAdvanced ? "收起 ▲" : "展開 ▼"}
                  onPress={() => setShowAdvanced(!showAdvanced)}
                  variant="secondary"
                  size="small"
                />
              </View>

              {showAdvanced && (
                <View style={styles.advancedOptions}>
                  <View style={styles.optionRow}>
                    <Text style={styles.optionSubLabel}>出題順序</Text>
                    <View style={styles.optionButtons}>
                      <Button
                        title="按順序"
                        onPress={() => setPracticeMode('sequential')}
                        variant={practiceMode === 'sequential' ? 'primary' : 'secondary'}
                        size="small"
                        style={styles.optionButton}
                      />
                      <Button
                        title="隨機"
                        onPress={() => setPracticeMode('random')}
                        variant={practiceMode === 'random' ? 'primary' : 'secondary'}
                        size="small"
                        style={styles.optionButton}
                      />
                    </View>
                  </View>
                </View>
              )}
            </Card>
          </View>

          {/* Bottom padding to account for sticky button */}
          <View className="h-[100px]" />
        </BottomSheetScrollView>

        {/* Sticky Bottom Button */}
        <View className="absolute bottom-0 left-0 right-0 p-5 bg-white border-t border-app-border">
          <Button
            title={selectedVolumes.length > 0 ? `開始練習 (已選 ${selectedVolumes.length} 冊)` : '開始練習'}
            onPress={handleStartPractice}
            disabled={selectedVolumes.length === 0 || loading}
            style={{
              backgroundColor: selectedVolumes.length > 0 ? COLORS.THEME : COLORS.CARD_BACKGROUND,
              borderColor: selectedVolumes.length > 0 ? COLORS.THEME : COLORS.BORDER
            }}
          />
        </View>
      </BottomSheetView>
    </BottomSheetModal>
  );
}

const styles = {
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  scrollView: {
    flex: 1,
  },
  bottomPadding: {
    height: 100, // Space for the sticky button
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.TEXT,
    letterSpacing: -0.3,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.BORDER,
    borderWidth: 0,
  },
  closeButtonText: {
    fontSize: 14,
    color: COLORS.TEXT,
    fontWeight: '600',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 6,
    color: COLORS.TEXT,
    letterSpacing: -0.3,
  },
  sectionSubtitle: {
    fontSize: 15,
    color: COLORS.TEXT_LIGHT,
    lineHeight: 20,
  },
  volumeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  volumeCard: {
    width: '48%',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: 16,
    backgroundColor: COLORS.CARD_BACKGROUND,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
    padding: 16,
  },
  selectedVolumeCard: {
    borderColor: COLORS.ACCENT,
    borderWidth: 3,
    shadowOpacity: 0.12,
    elevation: 4,
  },
  volumeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  volumeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  selectedVolumeTitle: {
    color: COLORS.TEXT,
    fontWeight: '700',
  },
  volumeSubtitle: {
    fontSize: 14,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  selectedVolumeSubtitle: {
    color: COLORS.TEXT,
  },
  volumeDescription: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
  },
  selectedVolumeDescription: {
    color: COLORS.SECONDARY_TEXT,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.CARD_BACKGROUND,
  },
  checkedBox: {
    backgroundColor: COLORS.ACCENT,
    borderColor: COLORS.ACCENT,
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statsContainer: {
    marginTop: 4,
  },
  accuracyText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  optionCard: {
    marginBottom: 12,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.TEXT,
  },
  optionSubLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT,
  },
  optionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 12,
  },
  advancedOptions: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  bottomButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 34,
    backgroundColor: COLORS.BACKGROUND,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    zIndex: 1000,
  },
  fullWidthButton: {
    width: '100%',
  },
  startButtonActive: {
    backgroundColor: COLORS.ACCENT,
  },
  startButtonActiveText: {
    color: 'white',
    fontWeight: '600',
  },
};