import React, { useRef } from 'react';
import {
    ActivityIndicator,
    Animated,
    Text,
    TextStyle,
    TouchableOpacity,
    View,
    ViewStyle,
} from 'react-native';

import { COLORS } from '../../utils/constants';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large' | 'custom';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  badge?: {
    count: number;
    color?: string;
  };
}

// Helper function to get button classes based on variant and size
const getButtonClasses = (variant: string, size: string, disabled: boolean) => {
  const baseClasses = "rounded-xl items-center justify-center shadow-sm";

  // Variant classes
  const variantClasses = {
    primary: "bg-app-theme border-app-theme",
    secondary: "bg-app-card border-app-border border",
    outline: "bg-transparent border-app-theme border"
  };

  // Size classes
  const sizeClasses = {
    small: "py-2 px-4 min-h-[36px]",
    medium: "py-4 px-5 min-h-[50px]",
    large: "py-[18px] px-6 min-h-[58px]"
  };

  const disabledClass = disabled ? "opacity-60" : "";

  return `${baseClasses} ${variantClasses[variant]} ${size !== 'custom' ? sizeClasses[size] : ''} ${disabledClass}`.trim();
};

// Helper function to get text classes based on variant and size
const getTextClasses = (variant: string, size: string, disabled: boolean) => {
  const baseClasses = "font-semibold text-center";

  // Variant text colors
  const variantTextClasses = {
    primary: "text-white",
    secondary: "text-app-text",
    outline: "text-app-theme"
  };

  // Size text classes
  const sizeTextClasses = {
    small: "text-sm",
    medium: "text-base",
    large: "text-lg"
  };

  const disabledClass = disabled ? "opacity-60" : "";

  return `${baseClasses} ${variantTextClasses[variant]} ${size !== 'custom' ? sizeTextClasses[size] : ''} ${disabledClass}`.trim();
};

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  badge,
}: ButtonProps) {
  const scaleAnimate = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (disabled || loading) {
      return;
    }

    Animated.spring(scaleAnimate, {
      toValue: 0.95,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handlePressOut = () => {
    if (disabled || loading) {
      return;
    }

    Animated.spring(scaleAnimate, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const animatedStyle = [
    { flex: 1, alignSelf: 'stretch' }, // Apply full width to animated wrapper
    {
      transform: [{ scale: scaleAnimate }],
    },
  ];

  const buttonClasses = getButtonClasses(variant, size, disabled);
  const textClasses = getTextClasses(variant, size, disabled);

  const showBadge = badge && badge.count > 0;

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        className={buttonClasses}
        style={style}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.9}
      >
        <View className="flex-row items-center justify-center relative">
          {loading ? (
            <ActivityIndicator
              size="small"
              color={variant === 'primary' ? '#ffffff' : COLORS.THEME}
            />
          ) : (
            <Text className={textClasses} style={textStyle}>{title}</Text>
          )}
        </View>
        {showBadge && (
          <View
            className="absolute -top-2 -right-2 rounded-[10px] min-w-[20px] h-5 justify-center items-center px-1 border-2 border-app-card"
            style={{ backgroundColor: badge?.color || COLORS.ERROR }}
          >
            <Text className="text-white text-[11px] font-bold text-center">
              {badge!.count.toString()}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}