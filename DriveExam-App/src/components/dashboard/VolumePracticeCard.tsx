import { useRouter } from 'expo-router';
import React, { useRef, useState } from 'react';
import { Alert, Animated, ImageBackground, Pressable, StyleSheet, Text, View } from 'react-native';
import { QuestionManager } from '../../services/questions/manager';
import { usePracticeStore } from '../../store/usePracticeStore';
import { PracticeConfig } from '../../types/session';
import { COLORS } from '../../utils/constants';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

// Volume illustration images mapping
const VOLUME_ILLUSTRATIONS = {
  1: require('../../../assets/images/illustrations/volume_1.png'),
  2: require('../../../assets/images/illustrations/volume_2.png'),
  3: require('../../../assets/images/illustrations/volume_3.png'),
  4: require('../../../assets/images/illustrations/volume_4.png'),
  5: require('../../../assets/images/illustrations/volume_5.png'),
} as const;

// Chinese number mapping
const CHINESE_NUMBERS = {
  1: '一',
  2: '二',
  3: '三',
  4: '四',
  5: '五',
} as const;

type VolumeStatsDisplay = {
  volume: number;
  title: string;
  correct: number;
  wrong: number;
  unseen: number;
  total: number;
};

type VolumePracticeCardProps = {
  volumeStats: VolumeStatsDisplay;
};

const VolumePracticeCard: React.FC<VolumePracticeCardProps> = ({ volumeStats }) => {
  const router = useRouter();
  const { startSession } = usePracticeStore();
  const [loading, setLoading] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const isDisabled = volumeStats.volume < 1;

  const handlePressIn = () => {
    if (isDisabled || loading) {
      return;
    }

    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handlePressOut = () => {
    if (isDisabled || loading) {
      return;
    }

    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handlePress = async () => {
    if (isDisabled) {
      Alert.alert('即將推出', '此冊別練習功能即將推出，敬請期待！');
      return;
    }

    setLoading(true);
    
    try {
      // Check if there are wrong questions to practice
      const hasWrongQuestions = volumeStats.wrong > 0;
      const hasUnseenQuestions = volumeStats.unseen > 0;
      
      const config: PracticeConfig = {
        volumes: [volumeStats.volume],
        chapter: null,
        mode: 'random',
        includeWrongQuestions: hasWrongQuestions,
        includeBookmarked: false,
        includeUnseen: hasUnseenQuestions, // Always include unseen questions if available
      };

      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '此冊別內沒有找到題目，請稍後再試。');
        return;
      }

      // Check if all questions have been mastered (answered correctly)
      const totalQuestions = volumeStats.total;
      const masteredQuestions = volumeStats.correct;
      const allMastered = masteredQuestions >= totalQuestions;
      
      if (allMastered) {
        Alert.alert(
          '恭喜完成！', 
          '您已掌握此冊的所有題目！現在將開始複習模式，您可以重新練習所有題目來保持熟練度。',
          [{ text: '開始複習', onPress: async () => {
            await startSession(config, questions);
            router.push('/practice/session');
          }}]
        );
        return;
      }

      // Show practice mode info
      if (hasWrongQuestions) {
        console.log(`Starting practice with ${volumeStats.wrong} wrong questions for volume ${volumeStats.volume}`);
      } else if (hasUnseenQuestions) {
        console.log(`Starting practice with ${volumeStats.unseen} unseen questions for volume ${volumeStats.volume}`);
      }

      await startSession(config, questions);
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start volume practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const progressData = [
    { value: volumeStats.correct, color: COLORS.SUCCESS }, // Green for correct
    { value: volumeStats.wrong, color: COLORS.ERROR },     // Red for wrong
    { value: volumeStats.unseen, color: COLORS.BORDER },   // Gray for unseen
  ];

  const backgroundImage = VOLUME_ILLUSTRATIONS[volumeStats.volume as keyof typeof VOLUME_ILLUSTRATIONS] || null;

  // Calculate question counts for display
  const remainingQuestions = volumeStats.unseen + volumeStats.wrong;
  const isCompleted = remainingQuestions === 0;
  const displayCount = isCompleted ? volumeStats.correct : remainingQuestions;
  const countLabel = isCompleted ? '已完成' : '剩';

  // Extract the common content area to avoid duplication
  const renderContent = () => (
    <View style={styles.contentContainer}>
      <Text style={[styles.subHeader, isDisabled && styles.textDisabled]}>
        第{CHINESE_NUMBERS[volumeStats.volume as keyof typeof CHINESE_NUMBERS] || volumeStats.volume}冊
      </Text>
      <View style={styles.titleRow}>
        <Text style={[styles.title, isDisabled && styles.textDisabled]}>
          {volumeStats.title}
        </Text>
        {!isDisabled && (
          <Text style={[styles.questionCount, isCompleted && styles.completedCount]}>
            {countLabel} {displayCount} 題
          </Text>
        )}
      </View>
      {isDisabled ? (
        <Text style={styles.comingSoonText}>即將推出</Text>
      ) : null}
      {!isDisabled && (
        <View style={styles.progressBarContainer}>
          {progressData.map((segment, index) => {
            const segmentWidth = (segment.value / volumeStats.total) * 100;
            if (segmentWidth === 0) return null;
            return (
              <View
                key={index}
                style={{
                  width: `${segmentWidth}%`,
                  height: '100%',
                  backgroundColor: segment.color,
                }}
              />
            );
          })}
        </View>
      )}
    </View>
  );

  const animatedStyle = {
    transform: [{ scale: scaleAnim }],
  };

  return (
    <AnimatedPressable
      style={[styles.card, animatedStyle]}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={loading}
    >
      <View
        style={[
          styles.cardContent,
          loading && styles.cardLoading,
          isDisabled && styles.cardDisabled
        ]}
      >
        {backgroundImage ? (
          <>
            <View style={styles.imageSection}>
              <ImageBackground
                source={backgroundImage}
                style={styles.imageBackground}
                imageStyle={styles.backgroundImage}
                resizeMode="cover"
              />
            </View>
            <View style={styles.textSection}>
              {renderContent()}
            </View>
          </>
        ) : (
          <View style={[styles.overlay, isDisabled && styles.overlayDisabled]}>
            {renderContent()}
          </View>
        )}
      </View>
    </AnimatedPressable>
  );
};

const styles = StyleSheet.create({
  card: {
    minHeight: 200,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#BFBFBF20',
    overflow: 'hidden',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 3,
    // backgroundColor: COLORS.CARD_BACKGROUND,
  },
  cardContent: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  imageSection: {
    height: 130,
    overflow: 'hidden',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  imageBackground: {
    flex: 1,
  },
  backgroundImage: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  textSection: {
    height: 70,
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
  },
  cardLoading: {
    opacity: 0.7,
  },
  cardDisabled: {
    opacity: 0.6,
    backgroundColor: COLORS.BORDER,
    borderColor: COLORS.BORDER,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(228, 228, 228, 0.2)',
    padding: 18,
    justifyContent: 'flex-end',
    borderRadius: 16,
  },
  overlayDisabled: {
    backgroundColor: COLORS.CARD_BACKGROUND_DISABLED,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  subHeader: {
    color: COLORS.THEME_TEXT,
    fontSize: 11,
    fontWeight: '700',
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  title: {
    color: COLORS.TEXT,
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 6,
    lineHeight: 16,
    textAlign: 'center',
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 3,
    overflow: 'hidden',
    flexDirection: 'row',
    marginTop: 6,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.3,
    shadowRadius: 1,
  },
  textDisabled: {
    opacity: 0.7,
  },
  comingSoonText: {
    color: COLORS.WARNING,
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'space-between',
  },
  questionCount: {
    fontSize: 11,
    fontWeight: '600',
    color: COLORS.WARNING,
    lineHeight: 14,
    textAlign: 'right',
  },
  completedCount: {
    color: COLORS.SUCCESS,
  },
});

export default VolumePracticeCard;
