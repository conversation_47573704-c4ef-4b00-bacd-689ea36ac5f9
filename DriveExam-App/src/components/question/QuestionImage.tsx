import React from 'react';
import {
    Image,
    View,
    ViewStyle,
} from 'react-native';
import { IMAGE_MAP, ImageKey } from '../../services/questions/imageMap';

interface QuestionImageProps {
  source: string;
  style?: ViewStyle;
}

export function QuestionImage({ source, style }: QuestionImageProps) {
  const resolvedSource = (IMAGE_MAP as Record<string, number>)[source as ImageKey] ?? { uri: source };
  return (
    <View className="w-full h-[200px] bg-gray-100 rounded-lg overflow-hidden" style={style}>
      <Image
        source={resolvedSource}
        className="w-full h-full"
        resizeMode="contain"
      />
    </View>
  );
}