import { LinearGradient } from 'expo-linear-gradient';
import React, { forwardRef } from 'react';
import { LayoutChangeEvent, NativeScrollEvent, NativeSyntheticEvent, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { COLORS } from '../../utils/constants';

export type QuestionState = 'unanswered' | 'answered' | 'correct' | 'wrong';

interface QuestionNumbersBarProps {
  totalQuestions: number;
  currentIndex: number;
  questionStates: QuestionState[];
  onQuestionPress: (index: number) => void;
  onLayout?: (event: LayoutChangeEvent) => void;
  onScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
}

export const QuestionNumbersBar = forwardRef<ScrollView, QuestionNumbersBarProps>(
  ({ totalQuestions, currentIndex, questionStates, onQuestionPress, onLayout, onScroll }, ref) => {

    return (
      <View style={styles.questionNumbersContainerWrapper}>
        {/* Left gradient fade */}
        <LinearGradient
          colors={['rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 0)']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.leftGradient}
        />

        {/* Right gradient fade */}
        <LinearGradient
          colors={['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.rightGradient}
        />

        <View style={styles.questionNumbersContainer}>
          <ScrollView
            ref={ref}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.questionNumbersContent}
            onLayout={onLayout}
            onScroll={onScroll}
            scrollEventThrottle={16}
          >
          {Array.from({ length: totalQuestions }, (_, index) => {
            const state = questionStates[index] || 'unanswered';
            const isCurrent = index === currentIndex;
            
            const getButtonStyle = () => {
              if (isCurrent) {
                return [styles.questionNumber, styles.questionNumberCurrent];
              }
              
              switch (state) {
                case 'answered':
                  return [styles.questionNumber, styles.questionNumberAnswered];
                case 'correct':
                  return [styles.questionNumber, styles.questionNumberCorrect];
                case 'wrong':
                  return [styles.questionNumber, styles.questionNumberWrong];
                default:
                  return styles.questionNumber;
              }
            };
            
            const getTextStyle = () => {
              if (isCurrent) {
                return [styles.questionNumberText, styles.questionNumberTextCurrent];
              }
              
              switch (state) {
                case 'answered':
                case 'correct':
                case 'wrong':
                  return [styles.questionNumberText, styles.questionNumberTextColored];
                default:
                  return styles.questionNumberText;
              }
            };
            
            return (
              <TouchableOpacity
                key={index}
                style={getButtonStyle()}
                onPress={() => onQuestionPress(index)}
              >
                <Text style={getTextStyle()}>
                  {index + 1}
                </Text>
              </TouchableOpacity>
            );
          })}
          </ScrollView>
        </View>
      </View>
    );
  }
);

QuestionNumbersBar.displayName = 'QuestionNumbersBar';

const styles = StyleSheet.create({
  questionNumbersContainerWrapper: {
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
    paddingVertical: 10,
    position: 'relative',
  },
  questionNumbersContainer: {
    position: 'relative',
  },
  leftGradient: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 20,
    zIndex: 1,
  },
  rightGradient: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: 40,
    zIndex: 1,
  },
  questionNumbersContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  questionNumber: {
    width: 36,
    height: 36,
    borderRadius: 4,
    backgroundColor: COLORS.BORDER,
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionNumberAnswered: {
    backgroundColor: COLORS.SUCCESS + '40',
  },
  questionNumberCorrect: {
    backgroundColor: COLORS.SUCCESS,
  },
  questionNumberWrong: {
    backgroundColor: COLORS.ERROR,
  },
  questionNumberCurrent: {
    backgroundColor: COLORS.THEME,
  },
  questionNumberText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.TEXT_LIGHT,
  },
  questionNumberTextColored: {
    color: 'white',
  },
  questionNumberTextCurrent: {
    color: 'white',
  },
}); 