import React from 'react';
import {
    Text,
    View,
} from 'react-native';
import { ProcessedQuestion } from '../../types/question';
import { Card } from '../common';
import { QuestionImage } from './QuestionImage';

interface QuestionCardProps {
  question: ProcessedQuestion;
  questionNumber: number;
  totalQuestions?: number;
}

export function QuestionCard({
  question,
  questionNumber,
  totalQuestions,
}: QuestionCardProps) {
  return (
    <Card className="mb-3">
      <View className="flex-row justify-between items-center mb-2">
        <Text className="text-base font-semibold text-app-theme-text">
          第 {questionNumber} 題{totalQuestions ? ` / 共 ${totalQuestions} 題` : ''}
        </Text>
        <Text className="text-sm text-gray-500">
          第 {question.volume} 冊
        </Text>
      </View>

      {question.image && (
        <QuestionImage
          source={question.image}
          style={{
            marginBottom: 4,
            backgroundColor: 'white',
            borderWidth: 1,
            borderColor: '#E5E5E5',
            borderRadius: 10,
            padding: 10
          }}
        />
      )}

      <View className="pt-2">
        <Text className="text-lg leading-6 text-gray-900 font-medium">
          {question.question}
        </Text>
      </View>
    </Card>
  );
}