import { setAlpha } from '@/src/utils/color';
import { COLORS } from '@/src/utils/constants';
import React, { useMemo, useRef } from 'react';
import {
  Animated,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { ProcessedQuestion } from '../../types/question';
import { createShuffledOptions } from '../../utils/questionUtils';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface OptionsListProps {
  options: ProcessedQuestion['options'];
  selectedOption?: number;
  onOptionSelect: (optionIndex: number) => void;
  showAnswer?: boolean;
  disabled?: boolean;
  disableShuffling?: boolean; // Add prop to control shuffling
}

type Variant = 'default' | 'selected' | 'correct' | 'wrong';

// Helper functions to get Tailwind classes based on variant
const getOptionClasses = (variant: Variant, disabled: boolean) => {
  const baseClasses = "rounded-xl border-2 bg-white p-4 py-3";

  const variantClasses = {
    default: "border-app-border",
    selected: "border-app-theme bg-app-card",
    correct: "border-green-500",
    wrong: "border-red-500 bg-red-50"
  };

  const disabledClass = disabled ? "opacity-80" : "";

  return `${baseClasses} ${variantClasses[variant]} ${disabledClass}`.trim();
};

const getLabelClasses = (variant: Variant) => {
  const baseClasses = "w-[26px] h-[26px] rounded-2xl items-center justify-center mr-3 border";

  const variantClasses = {
    default: "bg-gray-100 border-app-border",
    selected: `border-app-theme`,
    correct: `border-app-success`,
    wrong: `border-app-error`
  };

  return `${baseClasses} ${variantClasses[variant]}`.trim();
};

const getLabelTextClasses = (variant: Variant) => {
  const baseClasses = "text-base font-semibold";

  const variantClasses = {
    default: "text-gray-900",
    selected: "text-app-theme-text",
    correct: "text-app-success",
    wrong: "text-app-error"
  };

  return `${baseClasses} ${variantClasses[variant]}`.trim();
};

const getOptionTextClasses = (variant: Variant) => {
  const baseClasses = "flex-1 text-base leading-5";

  const variantClasses = {
    default: "text-gray-900",
    selected: "text-app-text",
    correct: "text-green-500",
    wrong: "text-red-500"
  };

  return `${baseClasses} ${variantClasses[variant]}`.trim();
};

export function OptionsList({
  options,
  selectedOption,
  onOptionSelect,
  showAnswer = false,
  disabled = false,
  disableShuffling = false,
}: OptionsListProps) {
  // Animation refs for each option (A, B, C, D)
  const scaleAnims = useRef(options.map(() => new Animated.Value(1))).current;

  // Create shuffled options and mappings - memoize to keep consistent during re-renders
  // Only shuffle if shuffling is not disabled
  const { shuffledOptions, mapShuffledToOriginal } = useMemo(
    () => disableShuffling ?
      {
        shuffledOptions: options,
        mapShuffledToOriginal: (index: number) => index
      } :
      createShuffledOptions(options),
    [options, disableShuffling] // Re-shuffle only when the actual options change or shuffling setting changes
  );

  const resolveVariant = (shuffledIndex: number): Variant => {
    // Convert shuffled index back to original to check if it's the selected option
    const originalIndex = mapShuffledToOriginal(shuffledIndex);
    const isSelected = selectedOption === originalIndex;
    const isCorrect = !!shuffledOptions[shuffledIndex]?.isCorrect;

    if (showAnswer && isCorrect) return 'correct';
    if (showAnswer && isSelected && !isCorrect) return 'wrong';
    if (isSelected) return 'selected';
    return 'default';
  };

  const handleOptionPressIn = (shuffledIndex: number) => {
    if (disabled) return;

    Animated.spring(scaleAnims[shuffledIndex], {
      toValue: 0.95,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handleOptionPressOut = (shuffledIndex: number) => {
    if (disabled) return;

    Animated.spring(scaleAnims[shuffledIndex], {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const renderOption = (option: { text: string; isCorrect: boolean }, shuffledIndex: number) => {
    const optionLabels = ['A', 'B', 'C', 'D'];
    const variant = resolveVariant(shuffledIndex);

    const handleOptionPress = () => {
      if (!disabled) {
        // Convert shuffled index back to original index before calling onOptionSelect
        const originalIndex = mapShuffledToOriginal(shuffledIndex);
        onOptionSelect(originalIndex);
      }
    };

    const animatedStyle = {
      transform: [{ scale: scaleAnims[shuffledIndex] }],
    };

    // Get background color for selected variant with alpha
    const getLabelBackgroundColor = () => {
      switch (variant) {
        case 'selected':
          return setAlpha(COLORS.THEME, 0.2);
        case 'correct':
          return setAlpha(COLORS.SUCCESS, 0.15);
        case 'wrong':
          return setAlpha(COLORS.ERROR, 0.15);
        default:
          return undefined;
      }
    };

    return (
      <AnimatedTouchableOpacity
        key={shuffledIndex}
        className={getOptionClasses(variant, disabled)}
        style={animatedStyle}
        onPress={handleOptionPress}
        onPressIn={() => handleOptionPressIn(shuffledIndex)}
        onPressOut={() => handleOptionPressOut(shuffledIndex)}
        disabled={disabled}
        activeOpacity={0.8}
      >
        <View className="flex-row items-center justify-center">
          <View
            className={getLabelClasses(variant)}
            style={{ backgroundColor: getLabelBackgroundColor() }}
          >
            <Text className={getLabelTextClasses(variant)}>{optionLabels[shuffledIndex]}</Text>
          </View>
          <Text className={getOptionTextClasses(variant)}>
            {option.text}
          </Text>
        </View>
      </AnimatedTouchableOpacity>
    );
  };

  return (
    <View className="gap-3 pb-4">
      {shuffledOptions.map(renderOption)}
    </View>
  );
}