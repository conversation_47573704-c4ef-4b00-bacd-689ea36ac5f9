import { create } from 'zustand';
import { VolumeStats, OverallStats } from '../types/statistics';

interface AppState {
  // Loading states
  isInitializing: boolean;
  isLoading: boolean;
  error: string | null;

  // Statistics
  volumeStats: VolumeStats[];
  overallStats: OverallStats | null;
  
  // UI state
  activeTab: 'dashboard' | 'practice' | 'history' | 'profile';
  modalVisible: boolean;
  modalContent: React.ReactNode | null;
  
  // Actions
  setInitializing: (loading: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setVolumeStats: (stats: VolumeStats[]) => void;
  setOverallStats: (stats: OverallStats) => void;
  setActiveTab: (tab: AppState['activeTab']) => void;
  showModal: (content: React.ReactNode) => void;
  hideModal: () => void;
  reset: () => void;
}

const initialState = {
  isInitializing: true,
  isLoading: false,
  error: null,
  volumeStats: [],
  overallStats: null,
  activeTab: 'dashboard' as const,
  modalVisible: false,
  modalContent: null,
};

export const useAppStore = create<AppState>((set) => ({
  ...initialState,

  setInitializing: (loading) => set({ isInitializing: loading }),
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  setError: (error) => set({ error }),
  
  setVolumeStats: (stats) => set({ volumeStats: stats }),
  
  setOverallStats: (stats) => set({ overallStats: stats }),
  
  setActiveTab: (tab) => set({ activeTab: tab }),
  
  showModal: (content) => set({ 
    modalVisible: true, 
    modalContent: content 
  }),
  
  hideModal: () => set({ 
    modalVisible: false, 
    modalContent: null 
  }),
  
  reset: () => set(initialState),
}));