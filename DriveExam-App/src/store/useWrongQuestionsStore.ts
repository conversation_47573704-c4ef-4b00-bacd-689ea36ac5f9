import { create } from 'zustand';
import { ProcessedQuestion } from '../types/question';
import { SessionStats } from '../types/session';

interface WrongQuestionsReviewState {
  // Session data
  isActive: boolean;
  isPaused: boolean;
  sessionId: string | null;
  startTime: Date | null;
  
  // Questions data
  questions: ProcessedQuestion[];
  currentIndex: number;
  currentQuestion: ProcessedQuestion | null;
  fixedTotalQuestions: number; // 固定的原始題目總數，不會因為答對而減少
  questionNumber: number; // 當前題目編號（1開始）
  
  // Current question state
  selectedAnswer: number | null;
  showAnswer: boolean;
  isAnswerCorrect: boolean | null;
  answerStartTime: Date | null;
  
  // Session stats
  stats: SessionStats;
  
  // UI state
  showExplanation: boolean;
  
  // Actions
  startReviewSession: (questions: ProcessedQuestion[]) => void;
  selectAnswer: (answerIndex: number) => void;
  submitAnswer: () => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  removeCurrentQuestion: () => void; // Remove when answered correctly
  pauseSession: () => void;
  resumeSession: () => void;
  endSession: () => void;
  toggleExplanation: () => void;
  reset: () => void;
}

const initialStats: SessionStats = {
  totalQuestions: 0,
  answeredCount: 0,
  correctCount: 0,
  wrongCount: 0,
  accuracy: 0,
  timeSpent: 0,
};

export const useWrongQuestionsStore = create<WrongQuestionsReviewState>((set, get) => ({
  isActive: false,
  isPaused: false,
  sessionId: null,
  startTime: null,
  questions: [],
  currentIndex: 0,
  currentQuestion: null,
  fixedTotalQuestions: 0,
  questionNumber: 1,
  selectedAnswer: null,
  showAnswer: false,
  isAnswerCorrect: null,
  answerStartTime: null,
  stats: initialStats,
  showExplanation: false,

  startReviewSession: (questions) => {
    const sessionId = `wrong_questions_review_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    set({
      isActive: true,
      isPaused: false,
      sessionId,
      startTime: new Date(),
      questions,
      currentIndex: 0,
      currentQuestion: questions[0] || null,
      fixedTotalQuestions: questions.length, // 儲存原始的題目總數，不會改變
      questionNumber: 1, // 初始化題目編號為1
      selectedAnswer: null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: new Date(),
      stats: {
        ...initialStats,
        totalQuestions: questions.length,
      },
      showExplanation: false,
    });
  },

  selectAnswer: (answerIndex) => {
    set({ selectedAnswer: answerIndex });
  },

  submitAnswer: () => {
    const { currentQuestion, selectedAnswer, stats } = get();
    
    if (!currentQuestion || selectedAnswer === null) return;

    const isCorrect = currentQuestion.options[selectedAnswer]?.isCorrect || false;
    
    set({
      showAnswer: true,
      isAnswerCorrect: isCorrect,
      stats: {
        ...stats,
        answeredCount: stats.answeredCount + 1,
        correctCount: stats.correctCount + (isCorrect ? 1 : 0),
        wrongCount: stats.wrongCount + (isCorrect ? 0 : 1),
        accuracy: ((stats.correctCount + (isCorrect ? 1 : 0)) / (stats.answeredCount + 1)) * 100,
      },
    });
  },

  nextQuestion: () => {
    const { questions, currentIndex, questionNumber } = get();
    const nextIndex = currentIndex + 1;
    
    if (nextIndex < questions.length) {
      set({
        currentIndex: nextIndex,
        currentQuestion: questions[nextIndex],
        questionNumber: questionNumber + 1, // 移到下一題編號
        selectedAnswer: null,
        showAnswer: false,
        isAnswerCorrect: null,
        answerStartTime: new Date(),
        showExplanation: false,
      });
    }
  },

  previousQuestion: () => {
    const { questions, currentIndex, questionNumber } = get();
    const prevIndex = currentIndex - 1;
    
    if (prevIndex >= 0) {
      set({
        currentIndex: prevIndex,
        currentQuestion: questions[prevIndex],
        questionNumber: Math.max(1, questionNumber - 1), // 回到上一題編號，最小為1
        selectedAnswer: null,
        showAnswer: false,
        isAnswerCorrect: null,
        answerStartTime: new Date(),
        showExplanation: false,
      });
    }
  },

  removeCurrentQuestion: () => {
    const { questions, currentIndex, stats, questionNumber, fixedTotalQuestions } = get();
    const updatedQuestions = [...questions];
    updatedQuestions.splice(currentIndex, 1);
    
    const newIndex = currentIndex >= updatedQuestions.length ? updatedQuestions.length - 1 : currentIndex;
    const newCurrentQuestion = updatedQuestions[newIndex] || null;

    let newQuestionNumber = questionNumber;
    if (!(fixedTotalQuestions < questionNumber + 1)) {
      newQuestionNumber = questionNumber + 1;
    }
    set({
      questions: updatedQuestions,
      currentIndex: Math.max(0, newIndex),
      currentQuestion: newCurrentQuestion,
      questionNumber: newQuestionNumber, // 答對後移到下一題編號
      selectedAnswer: null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: new Date(),
      showExplanation: false,
      stats: {
        ...stats,
        // totalQuestions: updatedQuestions.length,
      },
    });
  },

  pauseSession: () => {
    set({ isPaused: true });
  },

  resumeSession: () => {
    set({ isPaused: false });
  },

  endSession: () => {
    set({
      isActive: false,
      isPaused: false,
      sessionId: null,
      startTime: null,
    });
  },

  toggleExplanation: () => {
    const { showExplanation } = get();
    set({ showExplanation: !showExplanation });
  },

  reset: () => {
    set({
      isActive: false,
      isPaused: false,
      sessionId: null,
      startTime: null,
      questions: [],
      currentIndex: 0,
      currentQuestion: null,
      fixedTotalQuestions: 0,
      questionNumber: 1,
      selectedAnswer: null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: null,
      stats: initialStats,
      showExplanation: false,
    });
  },
})); 