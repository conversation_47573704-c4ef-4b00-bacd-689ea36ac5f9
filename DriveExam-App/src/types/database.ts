export interface AnswerRecord {
  id: number;
  question_id: number;
  volume: number;
  chapter: number;
  is_correct: boolean;
  mode: 'practice' | 'exam';
  session_id: string;
  selected_option: 'A' | 'B' | 'C' | 'D';
  correct_option: 'A' | 'B' | 'C' | 'D';
  time_spent: number;
  created_at: string;
}

export interface Session {
  id: string;
  type: 'practice' | 'exam';
  title: string;
  total_questions: number;
  correct_count: number;
  wrong_count: number;
  duration_seconds: number;
  volumes: string; // JSON array [1,2,3]
  config: string; // JSON practice settings or exam rules
  is_completed: boolean;
  created_at: string;
  completed_at?: string;
}

export interface VolumeProgress {
  volume: number;
  total_questions: number;
  seen_count: number;
  correct_count: number;
  wrong_count: number;
  last_practice?: string;
  updated_at: string;
}

export interface QuestionStats {
  question_id: number;
  volume: number;
  total_attempts: number;
  correct_attempts: number;
  wrong_attempts: number;
  last_attempted?: string;
  is_bookmarked: boolean;
  note?: string;
}