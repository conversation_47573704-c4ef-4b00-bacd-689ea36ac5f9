"use client";

import { MantineProvider, createTheme, Input } from "@mantine/core";
import { HeroUIProvider } from "@heroui/react";

import "@mantine/core/styles.css";
// import classes from "@/app/[locale]/layout.module.css";

const theme = createTheme({
    // components: {
        // Input: Input.extend({ classNames: classes }),
    // },
});

export default function UIProviders({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <HeroUIProvider>
            <MantineProvider theme={theme}>
				{children}
			</MantineProvider>
        </HeroUIProvider>
    );
}
