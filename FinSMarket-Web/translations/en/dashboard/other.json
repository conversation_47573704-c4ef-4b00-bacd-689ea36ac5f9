{"Chart": {"Label": {"open": "Open Price", "high": "High Price", "low": "Low Price", "close": "Close Price", "volume": "Trading Volume"}}, "AboutUs": {"Introduction": {"highlight": "ABOUT US", "label": "FinSMarket, Making Investing Simple", "description": "FinSMarket is a system platform that provides U.S. stock market predictions and analysis. We aim to help investors quickly understand market trends. Our goal is to leverage the power of AI and big data to fundamentally transform the vision of the stock market forecasting industry. In recent years, as AI large models have become widespread, AI has caused waves across various industries, changing the way businesses operate and heralding a future full of innovation and efficiency. We believe that with the right tools and algorithms, investors can gain a competitive advantage in the ever-changing financial markets."}, "AIDriven": {"label": "Built for Investors, Driven by AI", "point_1": "We believe in the power of AI to drive data-driven decisions and help investors achieve reliable and productive financial results.", "point_2": "We also believe that AI can help investment and trading teams inspire creativity and connect with unprecedented opportunities.", "point_3": "We are committed to helping our clients grow their wealth faster by unlocking the potential of AI and advanced analytics."}, "Team": {"CEO": {"label": "CEO Introduction", "name": "<PERSON>", "title": "CEO & Founder", "description": "Our CEO, Mr. <PERSON>, focuses on the development of AI, leading and inspiring the FinSMarket team to constantly push the limits of stock market forecasting with his financial background and passion for AI technology.", "quote": "At FinSMarket, we envision a future where every investor, regardless of their experience level, can access the power of AI-driven financial analysis. Our mission is to democratize financial intelligence and create a level playing field in the world of investing."}, "Shareholder": {"label": "Shareholder Introduction", "name": "ZentoAI", "description": "ZentoAI is our shareholder. ZentoAI is dedicated to the development of the AI industry in the Greater China region and the U.S. and European markets. Their rich experience and expertise in this field help shape FinSMarket's vision and guide our development."}}, "Statistics": {"ReportUsage": {"description": "Since the platform's launch, FinSMarket has accumulated over 600,000 page views, providing indispensable market insights to numerous investors."}, "EmailSubscriber": {"description": "Over 42,000 insightful investors trust FinSMarket's analysis reports to make smarter and more profitable decisions."}}, "Goal": {"highlight": "Our Goal", "label": "Building a Comprehensive Analysis and Forecasting Platform", "description": "Our current U.S. stock analysis and forecasting is just the beginning. In the future, we will continue to roll out analysis and forecasts for the futures market, currency market, bonds, and more. Stay tuned!"}, "Promise": {"label": "FinSMarket's Promise", "sentence_1": "We are committed to providing users with the highest quality information and analysis. We believe that by staying true to our mission and continuously innovating, we can help investors achieve their financial goals and create a more transparent and predictable stock market for everyone.", "sentence_2": "Finally, we would like to thank you again for visiting our website. We invite you to explore our insights and analyses and join us on our journey to revolutionize the stock market forecasting industry. For business collaborations, please contact us via our official email. Thank you!"}}, "Pricing": {"label": "Pricing Plans", "description": "Advanced features to help you make smart decisions", "Plan": {"Common": {"free": "Free", "comingSoon": "Coming Soon", "month": "Month", "year": "Year", "monthly": "Monthly", "yearly": "Yearly", "popular": "Popular Plan", "annual": "Annual", "savePercentage": "Save {value}", "getStarted": "Get Started", "registered": "Registered", "surveyCompleted": "Survey Completed", "pleaseLogin": "Please Login"}, "SurveyNotCompleted": {"label": "Registered User", "cta": "Register", "Feature": {"label": "Basic Features", "companyFundamental": "View company fundamental information (real-time)", "stockAnalysis": "FinS AI stock analysis function (up to {number} stocks/day)", "selectedReportTuesday": "Weekly potential stock selection report on Tuesday ({number} per month)", "selectedReportThursday": "Weekly premium stock selection report on Thursday ({number} per month)", "analysisReportSaturday": "Weekly stock analysis report on Saturday ({send_number} per month, up to {limit_number} stocks per week)"}}, "SurveyCompleted": {"label": "Registered User (Completed Survey)", "cta": "Fill Survey", "Feature": {"label": "Advanced Features", "description": "In addition to basic features:", "stockAnalysis": "FinS AI stock analysis function (up to {number} stocks/day)", "analysisReportSaturday": "Weekly stock analysis report on Saturday (sent until the end of {year}, up to {limit_number} stocks per week)", "manageReport": "Manage Saturday subscription reports (keep {month_number} months of records)"}}, "PaidUser": {"label": "Premium User", "cta": "Stay Tuned", "Feature": {"label": "Premium Features", "description": "In addition to advanced features:", "stockAnalysis": "FinS AI stock analysis function (unlimited access)", "analysisReportSaturday": "Weekly stock analysis report on Saturday (up to {limit_number} stocks per week)", "manageReport": "Manage Saturday subscription reports (keep {month_number} months of records)", "investingTool": "FinS AI investment portfolio tool", "aiPerspective": "FinS AI perspective analysis", "stockPriceNotice": "Stock target price notice", "newFeature": "Priority access to new features"}}}}, "ContactUs": {"label": "We'd Love to Hear From You", "description": "Want to get in touch? You can also reach us <NAME_EMAIL>", "Form": {"Name": {"label": "Name", "placeholder": "Please enter your name"}, "Email": {"label": "Email", "placeholder": "Please enter your email"}, "Message": {"label": "Your Message"}, "Topics": {"label": "What is this about?", "description": "You can select multiple (if applicable)"}, "CTA": {"send": "Send"}}, "Success": {"label": "Message Sent Successfully", "description": "Your support request has been sent. Our team will review it and get back to you shortly."}}, "MobileApp": {"label": "Mobile App", "badge": "NEW"}}