{"Dashboard": {"Searchbar": {"placeholder": "Search for company or stock symbol... (AAPL)", "noResult": "No search results found. Please try other stock symbol."}, "Sidebar": {"Category": {"basic": "FinS AI", "advanced": "Advanced", "other": "Other"}, "label": {"stockAnalysis": "Stock Analysis", "featuredStocks": "Featured Stocks", "analysisReport": "Analysis Report", "portfolioManager": "Portfolio Manager", "stockDetector": "Stock Detector", "pricing": "Pricing", "contactUs": "Contact Us", "membership": "Membership", "settings": "Settings"}, "report": {"label": "Report"}, "survey": {"label": "Survey"}}, "Profile": {"welcome": "Welcome", "description": "Manage your account settings and preferences."}, "Report": {"title": "Report", "description": "View your reports, manage reports perferences", "notSubscribed": {"title": "You haven't subscribe to the report", "description": "Please subscribe before access the reports and manage further perferences"}, "filledSurvey": {"description": "You have completed the survey, and the report subscription quantity has been increased to {max_number}."}, "notFilledSurvey": {"description": "After completing <link>the survey</link>, the report subscription quantity can be increased to {max_number}."}, "manage": {"title": "Manage your reports", "description": "To add or remove analysis reports", "welcome": "Welcome", "reportList": {"title": "Manage your reports", "description": "To add or remove analysis reports", "weeklyReport": "Weekly Report", "button": {"view": "View Report"}}, "update": {"title": "Modify Subscriptions", "description": "Update your preferences such as language and targeted stocks"}, "form": {"email": {"label": "Email", "placeholder": "Enter your email"}, "language": {"label": "Language", "placeholder": "Pick a language for future reports"}, "stocks": {"label": "Stocks Selection", "description": "Select up to {max_number} stocks"}}, "message": {"login": {"fail": {"title": "We encountered an error...", "description": "Please try again later or contact FinSMarket customer support."}}, "update": {"success": {"title": "Your preferences has been updated.", "description": "A confirmation email will arrive soon. Thanks for your continued support."}, "fail": {"title": "We encountered an error...", "description": "Please try again later or contact FinSMarket customer support."}}}, "button": {"manage": "Manage", "manageReport": "Member Registration", "update": "Update"}}}, "Survey": {"title": "Survey", "description": "Share with us your valuable ideas", "noSurveyAvailable": "Thank you for your time to complete the surveys, there's no survey available now", "message": {"submit": {"success": {"title": "Thank you for your help", "description": "Your valuable opions can help us make a better product together"}, "fail": {"title": "We're not able to collect the survey", "description": "Please try again later, thank you for your patience"}}}}, "Notice": {"Unregistered": {"title": "Unlock powerful stock analysis tools", "description": "Free registration to get: Company fundamentals analysis, 4 times stock analysis per week, Weekly selected reports, etc. Multiple quality services", "button": "Register Now"}, "GuestUserLimit": {"label": "Unlock More Stock Insights!", "description": "Sign up or log in to access detailed content on all stocks beyond AAPL. Don't miss out on the latest investment opportunities!", "CTA": {"signUp": "Sign Up for Free"}}, "ExceedLimit": {"label": "You've Reached Your Daily Limit!", "description": "To continue enjoying more content, you can get unlimited access by upgrading to a premium plan.", "descriptionFillSurvey": "Fill out a quick survey, answer a few questions, and your daily limit will be increased to 10 unique stocks per day.", "CTA": {"upgrade": "Upgrade Your Plan", "fillSurvey": "Fill out the survey"}}, "PremiumFeature": {"title": "Premium Feature", "description": "This content is available exclusively for our premium users. Upgrade your account to access this feature."}}, "StockUsageIndicator": {"label": "Daily limit", "stockViewedToday": "Stocks viewed today", "resetIn": "Limit resets at midnight in: {time}", "refreshToUpdate": "Please refresh the page to get the latest time"}}}