{"Portfolio": {"Statistics": {"cashBalance": "Cash Balance", "initialCashBalance": "Initial Cash Balance", "investedValue": "Invested Value", "realizedGain": "Realized Gain", "unrealizedGain": "Unrealized Gain", "PositionDistribution": {"title": "Portfolio Distribution", "description": "Current allocation", "Filters": {"includeCash": "Include Cash"}}, "Returns": {"title": "Portfolio Returns", "description": "View your portfolio's performance over time", "Filters": {"returns": "Returns", "returnRates": "Return Rates"}}}, "Status": {"Market": {"title": "Market Status", "opening": "Opening", "closing": "Closing", "closesAt": "Closes at {time} (your local time)", "opensAt": "Opens at {time} (your local time)"}}, "Operations": {"label": "Operations", "Common": {"buy": "Buy", "sell": "<PERSON>ll", "SelectStock": {"label": "Stock", "placeholder": "Select a stock"}, "Share": {"label": "Shares", "placeholder": "Enter number of shares"}, "latestPrice": "Latest Price", "totalValue": "Total Value", "max": "Max"}, "Create": {"label": "Create Position", "title": "Create New Position", "description": "Create a new position for your portfolio.", "Inputs": {"Symbol": {"label": "Stock Symbol", "placeholder": "Select a stock"}}}, "Edit": {"title": "Edit Position", "description": "Edit the shares for the position.", "Inputs": {"CurrentShare": {"label": "Current Shares"}}}}, "Table": {"symbol": "Symbol", "currentPriceAndAveragePrice": "Current Price / Average Price", "shares": "Shares", "profitLoss": "Profit / Loss", "actions": "Actions", "price": "Price", "operation": "Operation", "date": "Date", "buy": "Buy", "sell": "<PERSON>ll"}, "Common": {"noData": "No Data"}, "Errors": {"SharesMustBePositive": "Shares must be positive", "InsufficientFunds": "Insufficient balance", "InsufficientSharesToSell": "Insufficient shares to sell", "MarketClosed": "Trading is currently unavailable", "CompetitionInactive": "Competition is currently inactive", "MaxPositionCountReached": "Max position count reached, please sell some positions first", "CompetitionEnded": "Competition is ended, thank you for your participation"}, "ViewTransactions": {"tooltip": "View Transaction Records", "title": "Transaction Records", "description": "View all transaction records for this portfolio."}}}