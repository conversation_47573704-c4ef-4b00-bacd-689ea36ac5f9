{"Auth": {"signIn": {"title": "Sign In Your Account"}, "signUp": {"title": "Start Your Journey With Us"}, "form": {"email": {"label": "Email", "placeholder": "Enter your email"}, "firstName": {"label": "First Name", "placeholder": "Enter your first name"}, "lastName": {"label": "Last Name", "placeholder": "Enter your last name"}, "password": {"label": "Password", "description": "Password must contain: (1) A lowercase letter; (2) A captical letter; (3) A number; (4) Minimal 6 characters;", "placeholder": "Enter your password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Enter your password again"}, "button": {"signUp": "Sign Up", "signIn": "Sign In", "resetPassword": "Reset Password"}}, "message": {"signUp": {"userExists": {"title": "User already exists"}, "success": {"title": "Sign Up successfully"}, "fail": {"title": "Sign Up failed"}}, "signIn": {"userExists": "User already exists", "success": {"title": "Sign In successfully"}, "fail": {"title": "Sign In failed"}}, "incorrectCredential": "Incorrect Credential", "accountDeleted": "Your account has been successfully deleted"}, "button": {"google": "Continue with Google", "apple": "Continue with Apple", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "close": "Close Window", "backToHome": "Back to Home"}, "link": {"haveAccount": "Already have an account? Login here", "haveNoAccount": "Don't have an account? Register here", "backToSignIn": "Back to Sign In"}, "or": "or", "Email": {"Verify": {"label": "Welcome to FinSMarket!", "hi": "Hi", "description": "To complete your registration and enjoy our features, please verify your email address to proceed.", "resentIn": "Resend in {time}s", "CTA": {"send": "Send Verification Email", "resend": "Resend Verfication Email"}, "sentNotice": "We have sent an email to <email></email> with a verification link. Please check your email to verify your account."}, "Success": {"label": "Email Verified Successfully", "message": "Welcome to FinSMarket! Your email has been verified. Please sign in to continue.", "CTA": {"getStarted": "Get Started"}}, "Error": {"title": "Email Verification Failed", "invalidToken": "This verification link is either invalid or has expired. Please request a new verification email.", "generic": "Something went wrong while verifying your email. Please try again.", "CTA": {"tryAgain": "Back to Sign In"}}}, "resetPassword": {"title": "Reset Your Password", "success": {"title": "Password Reset Successfully", "description": "Your password has been reset. Please sign in with your new password."}, "fail": {"title": "Password Reset Failed", "description": "Something went wrong while resetting your password. Please try again."}, "invalidToken": {"title": "Invalid Reset Link", "description": "This password reset link is either invalid or has expired. Please request a new reset link."}}, "deleteAccount": {"title": "Delete Account", "description": "Once you delete your account, there is no going back. Please be certain.", "whatHappens": "What happens when you delete your account:", "consequence1": "Your account and all associated data will be permanently deleted", "consequence2": "You will be immediately logged out", "consequence3": "This action cannot be undone", "deleteButton": "Delete my account", "confirmTitle": "Are you absolutely sure?", "confirmDescription": "This action cannot be undone. This will permanently delete your account and remove your data from our servers.", "success": {"title": "Account Deleted Successfully", "description": "Your account has been permanently deleted", "message": "You will be automatically logged out. Thank you for using our service."}}}}