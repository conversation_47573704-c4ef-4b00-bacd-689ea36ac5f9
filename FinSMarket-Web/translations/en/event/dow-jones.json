{"DowJones": {"title": "Dow Jones Index Prediction Competition", "description": "Predict the Dow Jones Industrial Average closing price on specific dates", "Events": {"presidential_election_day": "Presidential Election Day", "q3_gdp_data_release": "2024 Q3 GDP Data Release Day", "non_farm_payroll_data_release": "Non-Farm Payroll Data Release Day", "federal_reserve_meeting": "Federal Reserve Meeting Day", "december_options_expiration": "December Options Expiration Day"}, "Rules": {"title": "Competition Rules", "item_1": "Participants need to predict the Dow Jones Index closing price on specified dates", "item_2": "The participant with the closest prediction to the actual closing price will win the prize", "item_3": "Each user can only submit one prediction", "item_4": "Predictions cannot be modified once submitted"}, "Prize": {"title": "Prize Structure", "first": "First Prize", "firstAmount": "100 USD", "firstPlace": "First Prize: 100 USD", "disclaimer": "Note: Actual prizes may vary depending on the specific event. Please refer to the prize shown on the event page."}, "Common": {"currentPrice": "Current Price"}, "Submission": {"submit": "Submit Prediction", "yourPrediction": "Your predicted closing price", "thankYou": "Thank you for participating!", "Confirmation": {"title": "Please confirm your prediction", "description": "Your prediction will be submitted and cannot be modified once submitted", "prediction": "Your prediction", "confirm": "Confirm", "cancel": "Cancel"}}, "Content": {"selectEvent": "Please select an event from the timeline", "invalidStatus": "Invalid event status"}, "Timeline": {"startTime": "Event Start Time", "completed": "Completed", "ongoing": "Ongoing", "upcoming": "Upcoming"}, "Countdown": {"days": "Days", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds"}, "CompletedEvent": {"prediction": "Prediction", "prize": "Prize", "result": "Result", "actualValue": "Actual Dow Jones Index Closing Price", "stillProcessing": "Still Processing, please wait"}, "SettlingEvent": {"title": "Settling Event", "description": "We are calculating the final results, please wait", "eventEndDate": "Event End Date", "estimatedSettlementTime": "Estimated Settlement Time"}, "OngoingEvent": {"loading": "Loading...", "submit": "Submit Prediction", "prizeStructure": "Prize Structure", "trophyAlt": "Trophy", "countdown": "Event End Countdown", "eventEndDate": "Event End Date"}, "FutureEvent": {"title": "Future Event", "startTime": "Event Start Time", "prizes": "Prize Structure", "countdown": "Event Start Countdown"}}}