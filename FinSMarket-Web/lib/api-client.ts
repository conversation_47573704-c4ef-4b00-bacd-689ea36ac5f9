import Axios, { InternalAxiosRequestConfig } from "axios";

function authRequestInterceptor(config: InternalAxiosRequestConfig) {
    if (config.headers) {
        config.headers.Accept = "application/json";
    }
    return config;
}

export const clientApi = Axios.create({
    baseURL: `${process.env.NEXT_PUBLIC_SITE_API_URL}/`,
    withCredentials: true,
});

clientApi.interceptors.request.use(authRequestInterceptor);
clientApi.interceptors.response.use(
    (response) => {
        return response.data;
    },
    (error) => {
        const message = error.response?.data?.message || error.message;
        console.log("Axios occurred error: ", message);

        return Promise.reject(error);
    }
);
