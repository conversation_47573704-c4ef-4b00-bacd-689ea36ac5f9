import nodemailer from "nodemailer";
import { v4 as uuidv4 } from "uuid";
import { prisma } from "@/lib/prisma";
import Mail from "nodemailer/lib/mailer";

const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: 465,
    secure: true,
    auth: {
        user: process.env.SMTP_USERNAME,
        pass: process.env.SMTP_PASSWORD,
    },
});

export async function sendEmail(
    from: string,
    to: string,
    subject: string,
    text: string,
    html: string,
    options: Mail.Options = {},
) {
    const mailOptions = {
        from: from,
        to: to,
        subject: subject,
        text: text,
        html: html,
        ...options,
    };

    await transporter.sendMail(mailOptions);
}

export const generateVerificationToken = (): string => {
    return uuidv4();
};

export const verifyEmailToken = async (token: string) => {
    const existingToken = await prisma.emailVerificationToken.findUnique({
        where: { token },
        include: { user: true }
    });

    if (!existingToken) {
        return { error: "Invalid token", success: false };
    }

    // Check if the token has expired
    const now = new Date();
    if (existingToken.expiresAt < now) {
        // Delete the expired token
        await prisma.emailVerificationToken.delete({
            where: { id: existingToken.id },
        });
        return { error: "Token has expired", success: false };
    }

    // Delete the used token
    await prisma.emailVerificationToken.delete({
        where: { id: existingToken.id },
    });

    await prisma.user.update({
        where: { id: existingToken.userId },
        data: { verifiedAt: new Date() },
    });

    return { 
        success: true,
        userId: existingToken.userId,
        email: existingToken.user.email
    };
};
