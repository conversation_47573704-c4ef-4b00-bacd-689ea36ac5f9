import WebSocket from "ws";
import { CustomStockData, globalStockData } from "@/server/stores/global-stock-data";
import { 
    isMarketOpen, 
    isHoliday, 
    COMPETITION_TIMEZONE,
    getNextMarketOpenTime
} from "@/utils/stock-market";
import { toZonedTime } from "date-fns-tz";
import { LogFile, getLogger } from "@/lib/logger";

const CENTRAL_WEBSOCKET_URL = "ws://aidatabase_wss.zentoai.com/service";
const SECRET_KEY = process.env.CENTRAL_WEBSOCKET_API_KEY;

export type CentralStockData = {
    [symbol: string]: number;
};

/**
 * @class CentralWebSocketHandler
 * @description Central WebSocket handler responsible for connection management, message transmission, and subscription management.
 */
class CentralWebSocketHandler {

    private static instance: CentralWebSocketHandler;
    private ws!: WebSocket;
    private statusLogger = getLogger(LogFile.WEBSOCKET);      // Status logger
    private detailLogger = getLogger(LogFile.WEBSOCKET_DETAIL); // Detailed logger
    private subscriptions: Map<string, Set<string>> = new Map(); // Subscription records: Stock code -> Set of user IDs
    private connectionQueue: (() => void)[] = [];              // Message queue
    private isConnected: boolean = false;                      // WebSocket connection status
    private isConnecting: boolean = false;                     // WebSocket connecting status
    private isAuthenticated: boolean = false;                  // Authentication status
    private subscribedTickers: Set<string> = new Set();          // Set of subscribed stock codes
    private reconnectTimer: NodeJS.Timeout | null = null;        // Reconnection timer
    private maxRetries = 20;                                   // Maximum retry attempts
    private currentRetries = 0;                                // Current retry count
    private baseRetryDelay = 1000;                             // Base retry delay (milliseconds)
    private maxRetryDelay = 10000;                             // Maximum retry delay (milliseconds)


    // =========================================================================
    // Section: Initialization and Singleton Pattern
    // =========================================================================

    
    /**
     * @static
     * @function getInstance
     * @description Get the single instance of CentralWebSocketHandler, implementing Singleton pattern.
     * @returns {CentralWebSocketHandler} Single instance
     */
    public static getInstance(): CentralWebSocketHandler {
        if (!CentralWebSocketHandler.instance) {
            CentralWebSocketHandler.instance = new CentralWebSocketHandler();
        }
        return CentralWebSocketHandler.instance;
    }
    


    /**
     * @constructor
     * @description Constructor, initializes WebSocket handler and establishes connection based on market status.
     */
    private constructor() {
        this.statusLogger.info('Initializing CentralWebSocketHandler');
        if (this.shouldConnect()) {
            this.statusLogger.info('Market is open, initializing WebSocket');
            this.initializeWebSocket();
        } else {
            this.statusLogger.info('Market is closed, scheduling next connection');
            this.scheduleNextConnection();
        }
    }
    


    // =========================================================================
    // Section: Connection Management
    // =========================================================================

    
    /**
     * @function shouldConnect
     * @description Check if a WebSocket connection should be established (based on market open status and holiday).
     * @returns {boolean} Returns true if market is open and not a holiday
     */
    private shouldConnect(): boolean {
        return isMarketOpen() && !isHoliday();
    }
    


    /**
     * @function initializeWebSocket
     * @description Initialize and establish WebSocket connection, and set various event handlers.
     */
    private initializeWebSocket() {
        // If already connected or connecting, skip initialization
        if (this.isConnected || this.isConnecting) {
            this.statusLogger.info(`WebSocket initialization skipped: ${
                this.isConnected ? 'already connected' : 
                this.isConnecting ? 'connection in progress' : 
                'development environment'
            }`);
            return;
        }
        
        // If retry count exceeds maximum, stop attempting connection and schedule next connection
        if (this.currentRetries >= this.maxRetries) {
            this.statusLogger.warn(`Max retries (${this.maxRetries}) reached. Stopping connection attempts.`);
            this.scheduleNextConnection();
            return;
        }

        this.isConnecting = true;
        this.statusLogger.info(`Initiating WebSocket connection (attempt ${this.currentRetries + 1}/${this.maxRetries})`);
        
        // Establish WebSocket connection, and authenticate via header
        this.ws = new WebSocket(CENTRAL_WEBSOCKET_URL, {
            headers: {
                "Authorization": SECRET_KEY
            }
        });

        // Connection success handler
        this.ws.on("open", () => {
            this.statusLogger.info("WebSocket connection established successfully");
            this.isConnecting = false;
            this.isConnected = true;
            this.isAuthenticated = true;  // Authenticated via header

            // If there are subscribed stocks, resubscribe
            if (this.subscribedTickers.size > 0) {
                this.detailLogger.info(`Resubscribing to ${this.subscribedTickers.size} tickers`);
                this.sendMessage({
                    command: "request_stock_price",
                    symbol_list: Array.from(this.subscribedTickers)
                });
            }

            // Process queued messages
            this.processQueue();
        });

        // Message reception handler
        this.ws.on("message", (data) => {
            try {
                this.handleMessage(data);
                this.processQueue();
            } catch (error) {
                this.statusLogger.error(`Failed to parse message: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        });

        // Connection closure handler
        this.ws.on("close", (code, reason) => {
            this.statusLogger.warn(`WebSocket connection closed. Code: ${code}, Reason: ${reason}`);
            this.isConnected = false;
            this.isAuthenticated = false;
            this.isConnecting = false;
            
            // If market is closed, clear all subscriptions
            if (!this.shouldConnect()) {
                this.clearSubscriptions();
            }
            // If reconnection is needed, can trigger reconnection mechanism here
            // this.handleReconnect(); // Commented out to prevent repeated reconnection
        });

        // Error handling
        this.ws.on("error", (error) => {
            this.statusLogger.error(`WebSocket error: ${error.message}`);
            this.isConnecting = false;
            this.handleReconnect();
        });
    }



    /**
     * @function clearSubscriptions
     * @description Clear all subscription data and reset connection status, usually used when market is closed.
     */
    private clearSubscriptions() {
        this.statusLogger.info('Clearing all subscriptions due to market closure');
        this.subscriptions.clear();
        this.subscribedTickers.clear();
        this.connectionQueue = [];
        this.isConnected = false;
        this.isConnecting = false;
        this.isAuthenticated = false;
        this.currentRetries = 0;  // Reset retry count
        
        // If WebSocket connection is still open, close connection
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.close(1000, "Market closed - clearing subscriptions");
        }
    }
    


    /**
     * @function scheduleNextConnection
     * @description Schedule next connection check based on next market open time, and delay check in holiday situations.
     */
    private scheduleNextConnection() {
        const now = new Date();
        const nextOpenTime = getNextMarketOpenTime();
        // Clear subscriptions and reset retry count before scheduling connection
        this.clearSubscriptions();

        if (isHoliday()) {
            this.statusLogger.info(`Holiday detected, scheduling check in 15 minutes at ${nextOpenTime}`);
        }

        const timeUntilNext = nextOpenTime.getTime() - now.getTime();

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.statusLogger.info('Cleared existing reconnect timer');
        }
        
        this.reconnectTimer = setTimeout(() => {
            if (this.shouldConnect() && !this.isConnected) {
                this.statusLogger.info('Market open detected, initiating connection');
                this.initializeWebSocket();
            } else {
                this.statusLogger.info('Market still closed, rescheduling check');
                this.scheduleNextConnection();
            }
        }, timeUntilNext);
        this.statusLogger.info(`Scheduling next connection check in ${Math.round(timeUntilNext / 60000)} min (${timeUntilNext/1000} seconds)`);
    }
    


    /**
     * @function handleReconnect
     * @description When connection fails, schedule reconnection using exponential backoff.
     */
    private handleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        // If market is closed, clear subscriptions and schedule next connection check
        if (!this.shouldConnect()) {
            this.statusLogger.info('Market closed, clearing subscriptions and scheduling next connection check');
            this.clearSubscriptions();
            this.scheduleNextConnection();
            return;
        }

        this.currentRetries++;
        const delay = Math.min(
            this.baseRetryDelay * Math.pow(2, this.currentRetries - 1),
            this.maxRetryDelay
        );

        this.statusLogger.info(`Scheduling reconnection attempt ${this.currentRetries} in ${delay/1000} seconds`);
        
        this.reconnectTimer = setTimeout(() => {
            if (this.shouldConnect() && !this.isConnected && !this.isConnecting) {
                this.initializeWebSocket();
            }
        }, delay);
    }
    


    // =========================================================================
    // Section: Message Transmission and Queue Management
    // =========================================================================


    /**
     * @function canSendMessage
     * @description Check if a message can be sent, based on connection and authentication status.
     * @param {boolean} needAuthenticated - Whether authentication is needed, default is true
     * @returns {boolean} Returns true if message can be sent
     */
    private canSendMessage(needAuthenticated: boolean = true) {
        return (
            this.isConnected && (!needAuthenticated || this.isAuthenticated) && this.shouldConnect()
        );
    }



    /**
     * @function sendMessage
     * @description Send message to WebSocket, if connection is not ready, add message to queue for later transmission.
     * @param {any} message - Message object to send
     * @param {boolean} needAuthenticated - Whether authentication is needed, default is true
     */
    private sendMessage(message: any, needAuthenticated: boolean = true) {
        const canSend = this.canSendMessage(needAuthenticated);
        if (canSend) {
            try {
                this.ws.send(JSON.stringify(message));
            } catch (error) {
                this.statusLogger.error(`Failed to send message: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
                // If sending fails and market is open, add message back to queue
                if (this.shouldConnect()) {
                    this.connectionQueue.push(() => { this.ws.send(JSON.stringify(message)) });
                }
            }
        } else if (this.shouldConnect()) {
            // Only queue message if market is open
            this.statusLogger.info('Message queued - connection not ready and market is open');
            this.connectionQueue.push(() => { this.ws.send(JSON.stringify(message)) });
        } else {
            this.statusLogger.info('Message dropped - market is closed');
        }
    }



    /**
     * @function processQueue
     * @description Process and send queued messages waiting to be sent, only when connection is normal.
     */
    private processQueue() {
        if (!this.canSendMessage()) {
            this.statusLogger.info(`Cannot process queue [${this.connectionQueue.length}] - connection not ready`);
            return;
        }

        while (this.connectionQueue.length > 0) {
            const sendFunc = this.connectionQueue.shift();
            if (sendFunc) {
                sendFunc();
            }
        }
    }



    // =========================================================================
    // Section: Message Handling
    // =========================================================================

    
    /**
     * @function handleMessage
     * @description Handle messages received from WebSocket and forward to stock update processing.
     * @param {WebSocket.Data} data - Received data
     */
    private handleMessage(data: WebSocket.Data) {
        let parsedData: CentralStockData;

        try {
            parsedData = JSON.parse(data.toString());
            // this.detailLogger.info(`Received stock update: ${JSON.stringify(parsedData)}`);
            this.handleStockUpdate(parsedData);
        } catch (error) {
            this.detailLogger.warn(`Failed to parse message as JSON: ${JSON.stringify(data.toString())}`);
            return;
        }
    }



    /**
     * @function handleStockUpdate
     * @description Handle stock price update and update global stock data.
     * @param {CentralStockData} data - Object containing stock code and price
     */
    private handleStockUpdate(data: CentralStockData) {
        Object.entries(data).forEach(([symbol, price]) => {
            const customData: CustomStockData = {
                symbol: symbol.toUpperCase(),  // Convert stock code to uppercase
                lastPrice: price,              // Latest price
                timestamp: new Date(),         // Update time
            };
            globalStockData.updateStock(customData);
        });
    }



    // =========================================================================
    // Section: Subscription Management
    // =========================================================================

    
    /**
     * @function subscribe
     * @description Subscribe specific stocks for a user and request stock price updates from WebSocket server.
     * @param {string} userId - User ID
     * @param {string[]} tickers - Array of stock codes
     */
    public subscribe(userId: string, tickers: string[]) {
        // Filter out new stock codes (unsubscribed parts)
        const newTickers = tickers.filter(ticker => !this.subscribedTickers.has(ticker.toUpperCase()));
        
        if (newTickers.length > 0) {
            // this.detailLogger.info(`User ${userId} subscribing to new tickers: ${newTickers.join(', ')}`);
            newTickers.forEach(ticker => this.subscribedTickers.add(ticker.toUpperCase()));
            this.sendMessage({
                command: "request_stock_price",
                symbol_list: Array.from(this.subscribedTickers)
            });
        }

        tickers.forEach(ticker => {
            if (!this.subscriptions.has(ticker)) {
                this.subscriptions.set(ticker, new Set());
            }
            this.subscriptions.get(ticker)!.add(userId);
        });
    }



    /**
     * @function unsubscribe
     * @description Unsubscribe a user from specific stocks and remove stock if no other subscribers.
     * @param {string} userId - User ID
     * @param {string[]} tickers - Array of stock codes
     */
    public unsubscribe(userId: string, tickers: string[]) {
        // this.detailLogger.info(`User ${userId} unsubscribing from tickers: ${tickers.join(', ')}`);
        tickers.forEach((ticker) => {
            const subscribers = this.subscriptions.get(ticker);
            if (subscribers) {
                subscribers.delete(userId);
                // If stock has no other subscribers, remove from subscription set
                if (subscribers.size === 0) {
                    this.subscriptions.delete(ticker);
                    this.subscribedTickers.delete(ticker.toUpperCase());
                }
            }
        });
    }



    /**
     * @function getSubscribedTickers
     * @description Get all stock codes subscribed by a specific user.
     * @param {string} userId - User ID
     * @returns {string[]} Array of stock codes subscribed by user
     */
    public getSubscribedTickers(userId: string): string[] {
        return Array.from(this.subscriptions.entries())
            .filter(([_, subscribers]) => subscribers.has(userId))
            .map(([ticker, _]) => ticker);
    }
}


// Export the single instance of CentralWebSocketHandler
export const centralWebSocketHandler = CentralWebSocketHandler.getInstance();
