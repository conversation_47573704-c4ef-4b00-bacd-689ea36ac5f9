import { prisma } from "@/lib/prisma";
import { AuthUser } from "@/types/model";
import { Message } from "@/lib/agent-cluster-api-client";
import { userHasPermissionTo } from "@/lib/user";
import { Permission } from "@prisma/client";

export const DEFAULT_DAILY_LIMIT = 10;
export const COMPLETED_SURVEY_DAILY_LIMIT = 20;

export const checkDailyUsage = async (
  user: AuthUser
): Promise<{ canSendMessage: boolean; currentCount: number; limit: number }> => {
  // Get today's date at the start of the day (following stock view usage pattern)
  const startOfToday = new Date();
  startOfToday.setHours(0, 0, 0, 0);

  const endOfToday = new Date();
  endOfToday.setHours(23, 59, 59, 999);

  const messageCount = await prisma.chatMessage.count({
    where: {
      chat: {
        userId: user.id,
      },
      role: "USER",
      createdAt: {
        gte: startOfToday,
        lte: endOfToday,
      },
      deletedAt: null,
    },
  });

  // Get user's daily limit based on permissions
  const dailyLimit = userHasPermissionTo(user.permissions, Permission.free_report_2024) 
    ? COMPLETED_SURVEY_DAILY_LIMIT 
    : DEFAULT_DAILY_LIMIT;

  return {
    canSendMessage: messageCount < dailyLimit,
    currentCount: messageCount,
    limit: dailyLimit,
  };
};

export const convertChatMessagesToApiMessages = (
  messages: Array<{ role: "USER" | "AI"; content: string }>
): Message[] => {
  return messages.map(message => ({
    role: message.role === "USER" ? "user" : "assistant",
    content: message.content,
  }));
};

export const generateChatTitle = (message: string): string => {
  // Simple title generation - take first 50 characters
  const title = message.trim().substring(0, 50);
  return title.length < message.trim().length ? `${title}...` : title;
}; 