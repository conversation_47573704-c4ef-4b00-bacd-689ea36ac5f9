import { env } from "@/config/env";

export interface Message {
  role: "user" | "assistant";
  content: string;
}

export interface ChatRequest {
  mission_type: "chat_with_agent_by_stream";
  category_name: "finsmarket";
  agent_name: "tabbi";
  messages: Message[];
}

export const makeAgentClusterApiCall = async (
  messages: Message[], 
  signal?: AbortSignal
) => {
  const response = await fetch(`${env.AGENT_CLUSTER_API_URL}/service/stream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': env.AGENT_CLUSTER_API_KEY,
    },
    body: JSON.stringify({
      mission_type: 'chat_with_agent_by_stream',
      category_name: 'finsmarket',
      agent_name: 'tabbi',
      messages: messages,
    } as ChatRequest),
    signal,
  });

  if (!response.ok) {
    throw new Error(`Agent Cluster API error: ${response.status} ${response.statusText}`);
  }

  return response;
}; 