import { apiPath } from "@/config/api";
import { aiDatabaseApi } from "@/lib/api/ai-database-api-client";
import UserRepo from "@/repository/user";
import { AddUserResponse } from "@/types/api/ai-database";

export const SyncService = {
    syncUser,
    syncAllUser,
}

async function syncUser(email: string) {
    const user = await UserRepo.getByEmail(email);

    if (!user) {
        console.warn("user not found:", email);
        return;
    }

    const response = await aiDatabaseApi.post<AddUserResponse>(apiPath.server.AI_DATABASE.addUser, {
        username: user.email,
        user_info_dict: user,
    });

    if (!response?.data?.response || !response?.data?.response?.status) {
        console.warn("syncing user failed:", response);
    }
}

async function syncAllUser() {
    const users = await UserRepo.getAll();

    await Promise.all(users.map(user => syncUser(user.email)));
    console.log(`synced ${users.length} user`);
}

export default SyncService;