import { create } from 'zustand';
import { CustomStockData } from '@/server/stores/global-stock-data';
import { isMarketOpen } from '@/utils/stock-market';

type StockStore = {
  stocks: { [ticker: string]: CustomStockData };
  updateStocks: (data: CustomStockData[]) => void;
  getStock: (ticker: string) => CustomStockData | undefined;
  subscribeToStocks: (userId: string, tickers: string[]) => void;
  unsubscribeFromStocks: () => void;
};

export const useStockStore = create<StockStore>((set, get) => ({
  stocks: {},
  
  updateStocks: (data: CustomStockData[]) => set((state) => {
    const newStocks = { ...state.stocks };
    data.forEach(stock => {
      newStocks[stock.symbol] = stock;
    });
    return { stocks: newStocks };
  }),
  
  getStock: (ticker: string) => get().stocks[ticker.toUpperCase()],
  
  subscribeToStocks: (userId: string, tickers: string[]) => {
    const tickerString = tickers.join(',');
    let retryCount = 0;
    const MAX_RETRIES = 5;
    const RETRY_DELAY = 5000; // 5 seconds

    const connect = () => {
      const eventSource = new EventSource(`/api/stock-prices?userId=${userId}&tickers=${tickerString}`);
      
      eventSource.addEventListener('stock_update', (event) => {
        const data: CustomStockData[] = JSON.parse(event.data);
        if (!Array.isArray(data) || data.length === 0) {
          // console.error("Invalid stock data: ", data);
          return;
        }
        get().updateStocks(data);
      });

      eventSource.addEventListener('heartbeat', () => {
        retryCount = 0;
      });

      eventSource.onerror = (error) => {
        // console.error('EventSource failed:', error);
        eventSource.close();

        if (!isMarketOpen()) {
          return;
        }

        if (retryCount < MAX_RETRIES) {
          retryCount++;
          // console.log(`Attempting to reconnect (${retryCount}/${MAX_RETRIES})...`);
          setTimeout(connect, RETRY_DELAY);
        } else {
          // console.error('Max retries reached. Giving up.');
        }
      };
      
      set((state) => ({ ...state, eventSource }));
    };

    connect();
  },
  
  unsubscribeFromStocks: () => {
    const { eventSource } = get() as any;
    if (eventSource) {
      eventSource.close();
    }
    set((state) => ({ ...state, eventSource: null }));
  }
}));