import { prisma } from "@/lib/prisma";
import { logError, AppError } from "@/lib/error-handler";
import { CompetitionRepo } from "./competition";
import StockRepo from "./stock";
import { VIRTUAL_TRADING_COMPETITION_PORTFOLIO_ID } from "@/config/constants";
import { PortfolioWithPositions, SnapshotPositionData } from "@/types/portfolio";
import { Portfolio, PortfolioSnapshot, Transaction, TransactionType, User } from "@prisma/client";
import { Decimal } from 'decimal.js';
import { endOfDay } from "date-fns";
import { startOfDay } from "date-fns";
import { DECIMAL_PRECISION } from "@/config/numbers";
import logger from "@/lib/logger";
import { isAfter, isSameDay } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { COMPETITION_TIMEZONE } from "@/utils/stock-market";

Decimal.set({ precision: DECIMAL_PRECISION });

export const PortfolioRepo = {
    getPortfolioByUserId,
    getUserPortfolioWithPositions,
    createDailySnapshot,
    isTodaySnapshotExists,
    getPortfolioSnapshotsByDateRange,
    getUserPortfolioSnapshots,
    calculatePortfolioReturns,
    updatePortfolioWithLatestPrices,
    closePortfolio,
    closeCompetitionPortfolios,
};

async function getPortfolioByUserId(userId: User["id"]): Promise<Portfolio | null> {
    return prisma.portfolio.findFirst({
        where: { userId },
    });
}

async function getUserPortfolioWithPositions(userId: User["id"], portfolioId: Portfolio["id"]) {
    try {
        let portfolio: PortfolioWithPositions | null = null;
        if (+portfolioId === VIRTUAL_TRADING_COMPETITION_PORTFOLIO_ID) {
            portfolio = await CompetitionRepo.getUserCompetitionPortfolio(userId);
        }
        else {
            portfolio = await prisma.portfolio.findFirst({
                where: { id: +portfolioId, userId },
                include: {
                    positions: true,
                },
            });

            if (portfolio) {
                portfolio.positions.sort((a, b) => 
                    (b.averagePrice * b.shares) - (a.averagePrice * a.shares)
                );
            }
        }

        if (!portfolio) {
            throw new AppError("Portfolio not found");
        }

        return portfolio;
    } catch (error) {
        logError(error, { operation: "getUserPortfolioWithPositions", userId });
        throw new AppError("Failed to get user portfolio with positions");
    }
}

async function createDailySnapshot(portfolioId: Portfolio["id"]): Promise<PortfolioSnapshot | null> {
    try {
        const portfolio = await prisma.portfolio.findUnique({
            where: { id: portfolioId },
            include: {
                positions: true,
            },
        });

        if (!portfolio) {
            throw new AppError("Portfolio not found");
        }

        const totalValue = new Decimal(portfolio.cashBalance)
            .plus(portfolio.investedValue)
            .plus(portfolio.unrealizedGain)
            .toNumber();

        const positionData: SnapshotPositionData[] = portfolio.positions.map(position => ({
            symbol: position.symbol,
            shares: position.shares,
            averagePrice: position.averagePrice,
            latestPrice: position.latestPrice,
            returns: new Decimal(position.latestPrice)
                .minus(position.averagePrice)
                .toNumber(),
            returnsRate: new Decimal(position.latestPrice)
                .minus(position.averagePrice)
                .dividedBy(position.averagePrice)
                .toNumber(),
        }));

        const returns = new Decimal(totalValue)
            .minus(portfolio.initialBalance)
            .toNumber();

        const returnRate = returns === 0 ? 0 : new Decimal(returns)
            .dividedBy(portfolio.initialBalance)
            .times(100)
            .toNumber();

        // Get today's date range
        const today = new Date();
        const startOfToday = startOfDay(today);
        const endOfToday = endOfDay(today);

        // Try to find existing snapshot
        const existingSnapshot = await prisma.portfolioSnapshot.findFirst({
            where: {
                portfolioId,
                date: {
                    gte: startOfToday,
                    lte: endOfToday,
                },
            },
        });

        if (existingSnapshot) {
            // Update existing snapshot
            return await prisma.portfolioSnapshot.update({
                where: { id: existingSnapshot.id },
                data: {
                    totalValue,
                    cashBalance: portfolio.cashBalance,
                    investedValue: portfolio.investedValue,
                    realizedGain: portfolio.realizedGain,
                    unrealizedGain: portfolio.unrealizedGain,
                    positionData: positionData,
                    returns: returns,
                    returnsRate: returnRate,
                },
            });
        }

        // Create new snapshot if none exists
        return await prisma.portfolioSnapshot.create({
            data: {
                portfolioId: portfolio.id,
                totalValue,
                cashBalance: portfolio.cashBalance,
                investedValue: portfolio.investedValue,
                realizedGain: portfolio.realizedGain,
                unrealizedGain: portfolio.unrealizedGain,
                positionData: positionData,
                returns: returns,
                returnsRate: returnRate,
            },
        });
    } catch (error) {
        logError(error, { operation: "createDailySnapshot", portfolioId });
        throw new AppError("Failed to create daily snapshot");
    }
}

async function isTodaySnapshotExists (portfolioId: Portfolio["id"]): Promise<boolean> {
    // const today = toZonedTime(new Date(), COMPETITION_TIMEZONE);
    const today = new Date();

    const snapshot = await prisma.portfolioSnapshot.findFirst({
        where: {
            portfolioId,
            date: {
                gte: startOfDay(today),
                lte: endOfDay(today),
            },
        },
    });

    return !!snapshot;
}

async function getPortfolioSnapshotsByDateRange(
    portfolioId: number, 
    startDate: Date, 
    endDate: Date
): Promise<PortfolioSnapshot[]> {
    try {
        const snapshots = await prisma.portfolioSnapshot.findMany({
            where: {
                portfolioId,
                date: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            orderBy: {
                date: 'asc',
            },
        });

        return snapshots;
    } catch (error) {
        logError(error, { operation: "getPortfolioSnapshots", portfolioId, startDate, endDate });
        throw new AppError("Failed to get portfolio snapshots");
    }
}

async function getUserPortfolioSnapshots(userId: User["id"], portfolioId: Portfolio["id"]): Promise<PortfolioSnapshot[]> {
    let competitionPortfolioId = portfolioId;
    if (+portfolioId === VIRTUAL_TRADING_COMPETITION_PORTFOLIO_ID) {
        const competitionPortfolio = await CompetitionRepo.getUserCompetitionPortfolio(userId);
        competitionPortfolioId = competitionPortfolio.id;
    }

    return prisma.portfolioSnapshot.findMany({
        where: {
            portfolioId: competitionPortfolioId,
            portfolio: {
                userId,
            },
        },
        orderBy: {
            date: 'asc',
        },
    });
}

async function calculatePortfolioReturns(
    portfolioId: number, 
    startDate: Date, 
    endDate: Date
): Promise<{
    overallReturn: number,
    overallReturnRate: number
} | null> {
    try {
        // 1. Get required data
        const snapshots = await getPortfolioSnapshotsByDateRange(portfolioId, startDate, endDate);
        if (snapshots.length === 0) {
            logger.info(`portfolioId [${portfolioId}]: No snapshots found`);
            return null;
        }

        const portfolio = await prisma.portfolio.findUnique({
            where: { id: portfolioId },
        });
        if (!portfolio) {
            throw new Error("Portfolio not found");
        }

        // 2. Determine start value based on snapshots
        const startSnapshot = snapshots[0];
        const endSnapshot = snapshots[snapshots.length - 1];
        const startValue = determineStartValue(startSnapshot, startDate, portfolio.initialBalance);

        // 3. Calculate returns
        const overallReturn = calculateReturn(endSnapshot.totalValue, startValue);
        const overallReturnRate = calculateReturnRate(overallReturn, startValue);

        return {
            overallReturn,
            overallReturnRate,
        };
    } catch (error) {
        logError(error, { operation: "calculatePortfolioReturns", portfolioId, startDate, endDate });
        throw new AppError("Failed to calculate portfolio returns");
    }
}

// Helper functions for clearer business logic
function determineStartValue(
    startSnapshot: PortfolioSnapshot, 
    startDate: Date, 
    initialBalance: number
): number {
    const startSnapshotDate = toZonedTime(startSnapshot.date, COMPETITION_TIMEZONE);
    const startDateZoned = toZonedTime(startDate, COMPETITION_TIMEZONE);

    const isStartSnapshotAfterStartDate = isAfter(startSnapshotDate, startDateZoned) && 
        !isSameDay(startSnapshotDate, startDateZoned);
    return isStartSnapshotAfterStartDate ? initialBalance : startSnapshot.totalValue;
}

function calculateReturn(endValue: number, startValue: number): number {
    return new Decimal(endValue)
        .minus(startValue)
        .toNumber();
}

function calculateReturnRate(returnValue: number, startValue: number): number {
    return new Decimal(returnValue)
        .dividedBy(startValue)
        .times(100)
        .toNumber();
}

async function updatePortfolioWithLatestPrices(portfolioId: Portfolio["id"]): Promise<Portfolio> {
    try {
        const portfolio = await prisma.portfolio.findUnique({
            where: { id: portfolioId },
            include: { positions: true },
        });

        if (!portfolio) {
            throw new AppError("Portfolio not found");
        }

        let totalInvestedValue = new Decimal(0);
        let totalUnrealizedGain = new Decimal(0);

        const updatedPositions = await Promise.all(portfolio.positions.map(async (position) => {
            const latestPrice = await StockRepo.getLatestStockPrice(position.symbol);
            
            if (latestPrice === null) {
                logger.error(`No price found for ${position.symbol} today. Skipping the position update...`);
                // throw new AppError(`Failed to get latest price for ${position.symbol}`);
                return null;
            }

            const currentValue = new Decimal(position.shares).times(latestPrice);
            const costBasis = new Decimal(position.shares).times(position.averagePrice);
            const unrealizedGain = currentValue.minus(costBasis);

            totalInvestedValue = totalInvestedValue.plus(costBasis);
            totalUnrealizedGain = totalUnrealizedGain.plus(unrealizedGain);

            return prisma.position.update({
                where: { id: position.id },
                data: { latestPrice },
            });
        }));

        const updatedPortfolio = await prisma.portfolio.update({
            where: { id: portfolioId },
            data: {
                investedValue: totalInvestedValue.toNumber(),
                unrealizedGain: totalUnrealizedGain.toNumber(),
            },
        });

        return updatedPortfolio;
    } catch (error) {
        logError(error, { operation: "updatePortfolioWithLatestPrices", portfolioId });
        throw new AppError("Failed to update portfolio with latest prices");
    }
}

async function closePortfolio(portfolioId: number): Promise<{
    success: boolean;
    error?: string;
    portfolio?: Portfolio;
}> {
    try {
        // Wrap everything in a transaction, to ensure consistency
        return await prisma.$transaction(async (tx) => {
            // 1. Get portfolio with positions
            const portfolio = await tx.portfolio.findUnique({
                where: { id: portfolioId },
                include: { 
                    positions: {
                        where: { shares: { gt: 0 } } // Only get positions with shares
                    }
                },
            });

            if (!portfolio) {
                throw new AppError(`Portfolio ${portfolioId} not found`);
            }

            // 2. Get closing prices for all positions at once
            const symbols = portfolio.positions.map(p => p.symbol);
            const closingPrices = await tx.stockPrice.findMany({
                where: {
                    stock: { symbol: { in: symbols } },
                    isClosingPrice: true,
                },
                include: { stock: {
                    select: { symbol: true },
                } },
                orderBy: { timestamp: 'desc' },
                distinct: ['stockId'],
            });

            // Create price lookup map
            const priceMap = new Map(
                closingPrices.map(price => [price.stock.symbol, price.price])
            );

            // 3. Process each position
            let totalRealizedGain = new Decimal(portfolio.realizedGain);
            let newCashBalance = new Decimal(portfolio.cashBalance);

            const transactions: Omit<Transaction, 'id' | 'timestamp'>[] = [];
            for (const position of portfolio.positions) {
                const closingPrice = priceMap.get(position.symbol);
                if (!closingPrice) {
                    logger.error(`No closing price found for ${position.symbol}`);
                    continue;
                }

                // Calculate position liquidation
                const proceeds = new Decimal(closingPrice).times(position.shares);
                const costBasis = new Decimal(position.averagePrice).times(position.shares);
                const positionRealizedGain = proceeds.minus(costBasis);

                // Update totals
                totalRealizedGain = totalRealizedGain.plus(positionRealizedGain);
                newCashBalance = newCashBalance.plus(proceeds);

                // Create sell transaction
                transactions.push({
                    portfolioId,
                    positionId: position.id,
                    symbol: position.symbol,
                    type: TransactionType.SELL,
                    shares: position.shares,
                    price: closingPrice,
                });
            }

            // 4. Bulk create transactions
            if (transactions.length > 0) {
                await tx.transaction.createMany({
                    data: transactions,
                });
            }

            // 5. Update portfolio
            const updatedPortfolio = await tx.portfolio.update({
                where: { id: portfolioId },
                data: {
                    cashBalance: newCashBalance.toNumber(),
                    realizedGain: totalRealizedGain.toNumber(),
                    investedValue: 0,
                    unrealizedGain: 0,
                    positions: {
                        updateMany: {
                            where: { portfolioId },
                            data: { shares: 0 }
                        }
                    }
                },
            });

            // 6. Create final snapshot (handled in other schedule job)
            // await createDailySnapshot(portfolioId);

            return {
                success: true,
                portfolio: updatedPortfolio
            };
        }, {
            maxWait: 10000, // 10s maximum wait time
            timeout: 30000  // 30s timeout
        });

    } catch (error) {
        logger.error('Failed to close portfolio', {
            portfolioId,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
        
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to close portfolio'
        };
    }
}

async function closeCompetitionPortfolios(competitionId: number) {
    // Get all portfolios for the competition
    const portfolios = await prisma.portfolio.findMany({
        where: {
            competitionEntry: {
                competitionId
            }
        },
        select: { id: true }
    });

    logger.info(`[CloseCompetitionPortfolios] portfolios: ${portfolios.length}`);

    const results = [];
    for (const portfolio of portfolios) {
        const result = await closePortfolio(portfolio.id);
        results.push({
            portfolioId: portfolio.id,
            ...result
        });
    }

    // Log summary
    const failed = results.filter(r => !r.success);
    if (failed.length > 0) {
        logger.error(`[CloseCompetitionPortfolios] Failed to close ${failed.length} portfolios`, { failed });
    }

    return results;
}

export default PortfolioRepo;
