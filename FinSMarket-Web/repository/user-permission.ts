import { prisma } from "@/lib/prisma";
import { Permission } from "@prisma/client";

export const UserPermissionRepo = {
    grant,
    remove,
}

async function grant(userId: string, permissionType: Permission | Array<Permission>, startDate?: string | Date, endDate?: string | Date) {
    if (Array.isArray(permissionType)) {
        await prisma.userPermission.createMany({
            data: permissionType.map(p => ({
                userId: userId,
                permission: p,
                startAt: startDate,
                endAt: endDate,
            })),
        });
    }
    else {
        await prisma.userPermission.create({
            data: {
                userId: userId,
                permission: permissionType,
                startAt: startDate,
                endAt: endDate,
            }
        });
    }
}

async function remove(userId: string, permissionType: Permission | Array<Permission>) {
        await prisma.userPermission.updateMany({
            where: {
                userId: userId,
                permission: Array.isArray(permissionType) 
                    ? 
                        {
                            in: permissionType,
                        }
                    :
                        permissionType,
            },
            data: {
                deleteAt: new Date(),
            },
        });
    // }
}

export default UserPermissionRepo;