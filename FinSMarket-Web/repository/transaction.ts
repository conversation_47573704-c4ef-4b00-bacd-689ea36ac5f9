import { prisma } from "@/lib/prisma";
import { logError, AppError } from "@/lib/error-handler";
import { TransactionType } from "@prisma/client";

export const TransactionRepo = {
    createTransaction,
    getTransactions,
};

async function createTransaction(
    portfolioId: number,
    positionId: number,
    symbol: string,
    type: TransactionType,
    shares: number,
    price: number
) {
    try {
        const transaction = await prisma.transaction.create({
            data: {
                portfolioId,
                positionId,
                symbol,
                type,
                shares,
                price,
            },
        });

        return transaction;
    } catch (error) {
        logError(error, { operation: "createTransaction", portfolioId, positionId, symbol });
        throw new AppError("Failed to create transaction");
    }
}

async function getTransactions(portfolioId: number) {
    try {
        const transactions = await prisma.transaction.findMany({
            where: { portfolioId: +portfolioId },
            orderBy: { timestamp: "desc" },
        });

        return transactions;
    } catch (error) {
        logError(error, { operation: "getTransactions", portfolioId });
        throw new AppError("Failed to get transactions");
    }
}

export default TransactionRepo;