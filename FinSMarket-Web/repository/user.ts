import { prisma } from "@/lib/prisma";
import { Argon2id } from "oslo/password";

export const UserRepo = {
    existsById,
    getByEmail,
    getAll,
    verifyPassword,
    updatePassword,
}

async function existsById(userId: string): Promise<boolean> {
    const user = await prisma.user.findFirst({
        where: {
            id: userId,
        },
    });

    return !!user;
}

async function getByEmail(email: string) {
    const user = await prisma.user.findFirst({
        where: {
            email: email,
        },
    });

    return user;
}

async function getAll() {
    const users = await prisma.user.findMany({
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            name: true,
            picture: true,
        },
    });

    return users;
}

async function verifyPassword(userId: string, password: string): Promise<boolean> {
    const user = await prisma.user.findFirstOrThrow({
        select: {
            hashedPassword: true,
        },
        where: {
            id: userId,
        }
    });

    if (!user.hashedPassword) return false;

    const passwordMatch = await new Argon2id().verify(user.hashedPassword, password);
    return passwordMatch;
}

async function updatePassword(userId: string, newPlainPassword: string): Promise<void> {
    const hashedPassword = await new Argon2id().hash(newPlainPassword);

    await prisma.user.update({
        where: { id: userId },
        data: { hashedPassword },
    })
}

export default UserRepo;