import { SubmissionPayload } from "@/components/profile/survey/tally-form";
import { locales } from "@/config/language";
import { getUser } from "@/lib/lucia";
import { prisma } from "@/lib/prisma";
import { Language } from "@prisma/client";

export const SurveyRepo = {
    getUserLatestSurvey,
    getTallyId,
    isUserFilled,
    storeSubmission,
}

async function getUserLatestSurvey(userId: string) {
    const survey = await prisma.survey.findFirst({
        where: {
            responses: {
                none: {
                    userId: userId,
                }
            }
        },
        orderBy: {
            createAt: "desc",
        },
    });

    return survey;
}

async function getTallyId(surveyId: string, locale: typeof locales[number]) {
    const survey = await prisma.surveyVersion.findFirst({
        where: {
            surveyId: surveyId,
            language: locale.replaceAll("-", "_") as Language,
        },
    });

    return survey?.tallyId;
}

async function isUserFilled(userId: string, tallyId: string): Promise<boolean> {
    const surveyVersion = await prisma.surveyVersion.findFirst({
        where: {
            tallyId: tallyId,
        }
    });

    if (!surveyVersion) {
        throw new Error("survey version not exists");
    }

    const userResponse = await prisma.userSurveyResponse.findFirst({
        where: {
            userId: userId,
            surveyId: surveyVersion?.surveyId,
        }
    })

    return !!userResponse;
}

async function storeSubmission(userId: string, tallyId: string, payload: SubmissionPayload) {
    const surveyVersion = await prisma.surveyVersion.findFirst({
        where: {
            tallyId: tallyId,
        },
    });

    if (!surveyVersion) {
        throw new Error("survey version not exists!");
    }

    const userResponse = await prisma.userSurveyResponse.create({
        data: {
            userId: userId,
            surveyId: surveyVersion.surveyId,
            surveyVersionId: surveyVersion.id,
            payload: payload as any,
        },
    });

    return {
        success: true,
        data: userResponse,
    }
}

export default SurveyRepo;