import { prisma } from "@/lib/prisma";
import { logError, AppError } from "@/lib/error-handler";
import { getOffsetUTCDate } from "@/utils/common";
import { PortfolioWithPositions } from "@/types/portfolio";
import { Competition, Portfolio, RankingInterval } from "@prisma/client";
import { PortfolioRankingWithUser } from "@/types/api/competition";
import { COMPETITION_TIMEZONE, getLastTradingDay } from "@/utils/stock-market";
import { CompetitionRankings } from "@/types/competition";
import { fromZonedTime, toZonedTime } from "date-fns-tz";
import { isBefore } from "date-fns";
import { isAfter } from "date-fns";


export const CompetitionRepo = {
    getCompetitionStatus,
    getCurrentCompetitionId,
    isAnyCompetitionActive,
    getCompetitionDetails,
    isUserAlreadyRegistered,
    registerUser,
    getUserCompetitionPortfolio,
    getAllCompetitionPortfolios,
    // getPortfolioSnapshots,
    updateRankings,
    getRankings,
    getAllRankings,
    getDateRangeByInterval,
};

export type CompetitionActiveStatus = {
    isActive: boolean;
    startDate: Date | null;
    endDate: Date | null;
    currentDate: Date | null;
    competition: Competition | null;
};

export const COMPETITION_UTC_OFFSET = -4; // UTC-4

async function getCompetitionStatus(date?: Date): Promise<CompetitionActiveStatus> {
    try {
        // const targetDate = toZonedTime(date || new Date(), COMPETITION_TIMEZONE);
        const targetDate = new Date();
        const competition = await prisma.competition.findFirst();

        if (!competition) {
            return {
                isActive: false,
                startDate: null,
                endDate: null,
                competition: null,
                currentDate: targetDate,
            };
        }

        const isActive = isAfter(targetDate, competition.startDate) && isBefore(targetDate, competition.endDate);

        return {
            isActive,
            startDate: competition.startDate,
            endDate: competition.endDate,
            currentDate: targetDate,
            competition,
        };
    } catch (error) {
        logError(error, { operation: "getCompetitionStatus" });
        throw new AppError("Failed to fetch competition status");
    }
}

async function getCurrentCompetitionId(): Promise<number> {
    try {
        const competition = await prisma.competition.findFirst();
        if (!competition) {
            throw new AppError("No active competition found");
        }
        return competition.id;
    } catch (error) {
        logError(error, { operation: "getCurrentCompetitionId" });
        throw new AppError("Failed to fetch current competition");
    }
}

async function isAnyCompetitionActive(): Promise<boolean> {
    const date = getOffsetUTCDate(COMPETITION_UTC_OFFSET);

    const competition = await prisma.competition.findFirst({
        where: {
            startDate: {
                lte: date,
            },
            endDate: {
                gte: date,
            },
        },
    });

    return !!competition;
}

async function isUserAlreadyRegistered(userId?: string): Promise<boolean> {
    if (!userId) return false;

    try {
        const competitionId = await getCurrentCompetitionId();
        const entry = await prisma.competitionEntry.findFirst({
            where: {
                portfolio: { userId },
                competitionId,
            },
        });

        return !!entry;
    } catch (error) {
        logError(error, { operation: "isUserAlreadyRegistered", userId });
        throw new AppError("Failed to check user registration", { userId });
    }
}

async function registerUser(userId: string): Promise<void> {
    if (!userId) throw new AppError("User ID is required");

    try {
        const competitionId = await getCurrentCompetitionId();
        await prisma.$transaction(async (prisma) => {
            const competition = await getCompetitionDetails(competitionId);
            const initialBalance = calculateInitialBalance(competition);

            const portfolio = await createPortfolio(
                prisma,
                userId,
                initialBalance
            );
            await createCompetitionEntry(prisma, portfolio.id, competitionId);
        });
    } catch (error) {
        logError(error, { operation: "registerUser", userId });
        throw new AppError("Failed to register user", { userId });
    }
}

async function getCompetitionDetails(competitionId?: number) {
    try {
        if (!competitionId) {
            competitionId = await getCurrentCompetitionId();
        }

        const competition = await prisma.competition.findUnique({
            where: { id: competitionId },
        });

        if (!competition) {
            throw new Error("Competition not found");
        }

        return competition;
    } catch (error) {
        logError(error, { operation: "getCompetitionDetails", competitionId });
        throw new AppError("Failed to get competition details", {
            competitionId,
        });
    }
}

function calculateInitialBalance(competition: {
    baseBalance: number;
    bonusAmount: number;
    bonusEndDate: Date | null;
}): number {
    const now = getOffsetUTCDate(COMPETITION_UTC_OFFSET);
    return (
        competition.baseBalance +
        (competition.bonusEndDate && now <= competition.bonusEndDate
            ? competition.bonusAmount
            : 0)
    );
}

async function createPortfolio(
    prisma: any,
    userId: string,
    initialBalance: number
) {
    return prisma.portfolio.create({
        data: {
            userId,
            initialBalance,
            cashBalance: initialBalance,
        },
    });
}

async function createCompetitionEntry(
    prisma: any,
    portfolioId: number,
    competitionId: number
) {
    await prisma.competitionEntry.create({
        data: {
            portfolioId,
            competitionId,
        },
    });
}

async function getUserCompetitionPortfolio(userId: string): Promise<PortfolioWithPositions> {
    try {
        const latestCompetitionEntry = await prisma.competitionEntry.findFirst({
            where: {
                portfolio: {
                    userId: userId,
                },
            },
            orderBy: {
                createdAt: 'desc', // Get the latest entry
            },
            include: {
                portfolio: {
                    include: {
                        positions: true,
                    },
                },
            },
        });

        if (!latestCompetitionEntry) throw new AppError("No competition portfolio found");

        return latestCompetitionEntry.portfolio;
    } catch (error) {
        logError(error, { operation: "getUserCompetitionPortfolio", userId });
        throw new AppError("Failed to get user competition portfolio");
    }
}

async function getAllCompetitionPortfolios(): Promise<Portfolio[]> {
    try {
        const portfolios = await prisma.portfolio.findMany({
            where: {
                competitionEntry: {
                    isNot: null,
                },
            },
        });

        return portfolios;
    } catch (error) {
        logError(error, { operation: "getAllCompetitionPortfolio" });
        throw new AppError("Failed to get all competition portfolios");
    }
}

// async function getPortfolioSnapshots(startDate: Date, endDate: Date) {
//     try {
//         return await prisma.portfolioSnapshot.findMany({
//             where: {
//                 date: {
//                     gte: startDate,
//                     lte: endDate,
//                 },
//             },
//             orderBy: {
//                 date: 'asc',
//             },
//             include: {
//                 portfolio: true,
//             },
//         });
//     } catch (error) {
//         logError(error, { operation: "getPortfolioSnapshots", startDate, endDate });
//         throw new AppError("Failed to get portfolio snapshots");
//     }
// }

async function updateRankings(rankings: CompetitionRankings, interval: RankingInterval, date: Date) {
    try {
        await prisma.$transaction(
            rankings.map((ranking) =>
                prisma.portfolioRanking.upsert({
                    where: {
                        portfolioId_interval_date: {
                            portfolioId: ranking.portfolioId,
                            interval: interval,
                            date: date,
                        },
                    },
                    update: {
                        rank: ranking.rank,
                        returnRate: ranking.returnRate,
                        returns: ranking.returns,
                    },
                    create: {
                        portfolioId: ranking.portfolioId,
                        rank: ranking.rank,
                        returnRate: ranking.returnRate,
                        returns: ranking.returns,
                        interval: interval,
                        date: date,
                    },
                })
            )
        );
    } catch (error) {
        logError(error, { operation: "updateRankings", interval, date });
        throw new AppError("Failed to update rankings");
    }
}

async function getRankings(
    interval: RankingInterval, 
    date?: Date, 
    size: number = 10,
    prismaClient: any = prisma, // Default to global prisma instance,
    minRate: number = 0.01
): Promise<PortfolioRankingWithUser[]> {
    let targetDate: Date;

    if (!date) {
        const latestRanking = await prismaClient.portfolioRanking.findFirst({
            where: { interval },
            orderBy: { date: 'desc' },
            select: { date: true },
        });

        if (!latestRanking) {
            return [];
            // throw new AppError("No rankings found for the given interval");
        }

        targetDate = latestRanking.date;
    } else {
        targetDate = new Date(date);
        targetDate.setHours(0, 0, 0, 0);
    }

    const endDate = new Date(targetDate);
    endDate.setDate(endDate.getDate() + 1);
    endDate.setMilliseconds(endDate.getMilliseconds() - 1);

    return await prismaClient.portfolioRanking.findMany({
        where: { 
            interval, 
            date: { 
                gte: targetDate,
                lt: endDate 
            },
            returnRate: {
                gte: minRate,
            },
        },
        include: {
            portfolio: {
                select: {
                    user: {
                        select: {
                            name: true,
                            firstName: true,
                            lastName: true,
                            picture: true,
                        },
                    },
                },
            },
        },
        orderBy: { rank: 'asc' },
        take: size,
    });
}

export type AllRankings = {
    daily: PortfolioRankingWithUser[] | [];
    weekly: PortfolioRankingWithUser[] | [];
    monthly: PortfolioRankingWithUser[] | [];
};

async function getAllRankings(date?: Date, size: number = 10, minRate: number = 0.01): Promise<AllRankings> {
    const rankings = await prisma.$transaction(async (prismaTransaction) => {
        const dailyRankings = await getRankings(RankingInterval.DAILY, date, size, prismaTransaction, minRate);
        const weeklyRankings = await getRankings(RankingInterval.WEEKLY, date, size, prismaTransaction, minRate);
        const monthlyRankings = await getRankings(RankingInterval.MONTHLY, date, size, prismaTransaction, minRate);
        return {
            daily: dailyRankings,
            weekly: weeklyRankings,
            monthly: monthlyRankings,
        };
    }, {
        timeout: 30000 // Increase timeout to 30 seconds
    });

    return rankings;
}

function getDateRangeByInterval(interval: RankingInterval, currentDate: Date): { startDate: Date, endDate: Date } {
    const endDate = new Date(currentDate);
    endDate.setHours(23, 59, 59, 999);

    let startDate = getStartDateByInterval(interval, endDate);
    startDate = getLastTradingDay(startDate);
    startDate.setHours(0, 0, 0, 0);

    return { startDate, endDate };
}

function getStartDateByInterval(interval: RankingInterval, date: Date): Date {
    const startDate = new Date(date);
    
    switch (interval) {
        case RankingInterval.DAILY:
            const day = startDate.getDay();
            const daysToSubtract = getDaysToSubtractForDaily(day);
            startDate.setDate(startDate.getDate() - daysToSubtract);
            break;

        case RankingInterval.WEEKLY:
            // Get last Friday (current day - weekday - 2)
            startDate.setDate(startDate.getDate() - startDate.getDay() - 2);
            break;

        case RankingInterval.MONTHLY:
            // Go to the last day of previous month
            startDate.setDate(0);
            break;
    }

    return startDate;
}

function getDaysToSubtractForDaily(day: number): number {
    switch (day) {
        case 0: // Sunday
            return 2;
        case 1: // Monday
            return 3;
        case 6: // Saturday
            return 1;
        default: // Regular weekday
            return 1;
    }
}

export default CompetitionRepo;
