import {
    PrismaClient,
    Stock,
    StockExchange,
    StockType,
} from "@prisma/client";
import { FMPStockListItem } from "../types/api/stocks";
import { isMarketOpen } from "../utils/stock-market";
import { exchangeCountryMapping } from "../constants/exchange-mapping";
import { startOfDay } from "date-fns";
import { globalStockData } from "@/server/stores/global-stock-data";
import { StockWithExchange } from "@/types/models/stock";

const prisma = new PrismaClient();

const StockRepo = {
    searchStocks,
    createOrUpdateStocks,
    createOrUpdateStocksPrice,
    getOrCreateExchange,
    mapStockType,
    getExchangeCountry,
    getLatestStockPrice,
};

async function searchStocks(
    query: string,
    options: {
        type?: StockType[];
        limit?: number;
        offset?: number;
    } = {}
): Promise<StockWithExchange[]> {
    const { limit = 10, offset = 0, type } = options;

    if (!query.trim()) {
        return [];
    }

    return prisma.stock.findMany({
        where: {
            AND: [
                {
                    OR: [
                        {
                            symbol: {
                                contains: query.toUpperCase(),
                                mode: 'insensitive',
                            },
                        },
                        {
                            name: {
                                contains: query,
                                mode: 'insensitive',
                            },
                        },
                    ],
                },
                ...(type && type.length > 0 
                    ? [{
                        type: {
                            in: type
                        }
                    }] 
                    : []
                ),
            ],
        },
        take: limit,
        skip: offset,
        select: {
            id: true,
            symbol: true,
            name: true,
            type: true,
            exchange: {
                select: {
                    code: true,
                    name: true,
                },
            },
        },
        orderBy: [
            {
                symbol: 'asc',
            },
        ],
    });
}

async function createOrUpdateStocks(
    stocks: FMPStockListItem[],
    updatePrice: boolean = false,
    country: "US" | "all" = "US",
    type: "all" | "stock" | "etf" | "trust" = "stock",
): Promise<number[]> {
    // Pre-fetch all exchanges
    const allExchanges = await prisma.stockExchange.findMany();
    const exchangeMap = new Map(
        allExchanges.map((exchange) => [exchange.code, exchange])
    );

    const stocksToCreate: Omit<Stock, "id">[] = [];
    const stocksToUpdate: Stock[] = [];

    // Filter stocks based on country
    const filteredStocksByCountry: FMPStockListItem[] =
        country === "all"
            ? stocks
            : stocks.filter((stock) => {
                  const exchanges = exchangeCountryMapping[country] || [];
                  return (exchanges as readonly string[]).includes(
                      stock.exchangeShortName
                  );
              });

    // Filter stocks based on type
    const filteredStocks: FMPStockListItem[] =
        type === "all"
            ? filteredStocksByCountry
            : filteredStocksByCountry.filter((stock) => {
                  return stock.type.toUpperCase() === type.toUpperCase();
              });

    // Fetch all existing stocks and create a map in one step
    const existingStockMap = new Map(
        (
            await prisma.stock.findMany({
                where: {
                    exchange: {
                        country: country === "all" ? undefined : country,
                    },
                },
            })
        ).map((stock) => [stock.symbol, stock])
    );

    for (const stockData of filteredStocks) {
        let exchange = exchangeMap.get(stockData.exchangeShortName);
        if (!exchange) {
            exchange = await getOrCreateExchange(
                stockData.exchangeShortName,
                exchangeMap
            );
            exchangeMap.set(stockData.exchangeShortName, exchange);
        }

        const existingStock = existingStockMap.get(stockData.symbol);
        const stockType = mapStockType(stockData.type);

        if (existingStock) {
            if (
                existingStock.name !== stockData.name ||
                existingStock.type !== stockType ||
                existingStock.exchangeId !== exchange.id
            ) {
                stocksToUpdate.push({
                    ...existingStock,
                    name: stockData.name,
                    type: stockType,
                    exchangeId: exchange.id,
                });
            }
        } else {
            if (
                !stockData.name ||
                !stockData.symbol ||
                !stockData.price ||
                !stockData.exchangeShortName
            ) {
                continue;
            }

            stocksToCreate.push({
                symbol: stockData.symbol,
                name: stockData.name || "missing stock name",
                type: stockType,
                exchangeId: exchange.id,
            });
        }
    }

    // Perform batch operations
    await prisma.$transaction([
        prisma.stock.createMany({
            data: stocksToCreate,
            skipDuplicates: true,
        }),
        ...stocksToUpdate.map((stock) =>
            prisma.stock.update({
                where: { id: stock.id },
                data: {
                    name: stock.name,
                    type: stock.type,
                    exchangeId: stock.exchangeId,
                },
            })
        ),
    ]);

    if (updatePrice) {
        await createOrUpdateStocksPrice(filteredStocks, !isMarketOpen());
    }

    return [stocksToCreate.length, stocksToUpdate.length];
}

async function createOrUpdateStocksPrice(
    stocks: FMPStockListItem[],
    isClosingPrice: boolean = false
): Promise<void> {
    // Fetch all stocks to get their IDs
    const existingStocks = await prisma.stock.findMany({
        where: {
            symbol: {
                in: stocks.map(s => s.symbol)
            }
        },
        select: {
            id: true,
            symbol: true
        }
    });

    const stockIdMap = new Map(existingStocks.map(s => [s.symbol, s.id]));

    const stockPrices = stocks
        .filter(stock => stock.price !== undefined && stock.price !== null && stockIdMap.has(stock.symbol))
        .map(stock => ({
            stockId: stockIdMap.get(stock.symbol)!,
            price: stock.price,
            isClosingPrice,
        }));

    // TODO - don't create dulplicated record if today's stock price exists
    await prisma.stockPrice.createMany({
        data: stockPrices,
        skipDuplicates: true,
    });
}

async function getOrCreateExchange(
    exchangeCode: string,
    exchangeMap?: Map<string, StockExchange>
): Promise<StockExchange> {
    if (exchangeMap && exchangeMap.has(exchangeCode)) {
        return exchangeMap.get(exchangeCode)!;
    }

    let exchange = await prisma.stockExchange.findUnique({
        where: { code: exchangeCode },
    });
    if (!exchange) {
        exchange = await prisma.stockExchange.create({
            data: {
                code: exchangeCode,
                name: exchangeCode, // TODO: Map to full name
                country: getExchangeCountry(exchangeCode),
            },
        });
    }
    return exchange;
}

function mapStockType(type: string): StockType {
    switch (type.toUpperCase()) {
        case "STOCK":
            return StockType.STOCK;
        case "ETF":
            return StockType.ETF;
        case "TRUST":
            return StockType.TRUST;
        default:
            return StockType.OTHER;
    }
}

function getExchangeCountry(exchangeCode: string): string | null {
    for (const [country, exchanges] of Object.entries(exchangeCountryMapping)) {
        if ((exchanges as readonly string[]).includes(exchangeCode)) {
            return country;
        }
    }
    return null;
}

async function getLatestStockPrice(symbol: string): Promise<number | null> {
    const today = startOfDay(new Date());
    
    const latestPrice = await prisma.stockPrice.findFirst({
        where: {
            stock: { symbol },
            timestamp: { gte: today },
        },
        orderBy: { timestamp: 'desc' },
    });

    if (!latestPrice) {
        // TODO: Implement logic to handle cases where today's price is not available
        const globalStockPrice = await globalStockData.getStock(symbol);
        if (globalStockPrice?.lastPrice) {
            return globalStockPrice.lastPrice;
        }

        const mostRecentPrice = await prisma.stockPrice.findFirst({
            where: { stock: { symbol } },
            orderBy: { timestamp: 'desc' },
        });

        if (!mostRecentPrice || !mostRecentPrice.price) {
            return null;
        }

        return mostRecentPrice.price;
    }

    return latestPrice.price;
}

export default StockRepo;
