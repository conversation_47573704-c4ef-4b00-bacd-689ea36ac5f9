import { PrismaClient } from "@prisma/client";
import { DowJonesPredictionEvent, UserDowJonesPrediction, User, } from "@prisma/client";
import { AppError } from "@/lib/error-handler";
import { getOffsetUTCDate } from "@/utils/common";
import { z } from "zod";
import { EventWithPrediction } from "@/types/models/prediction-event";
import { UserPredictionSchema } from "@/types/validations/competition";
import { COMPETITION_UTC_OFFSET } from "./competition";
import { UserForPublic } from "@/types/models/user";
import logger from "@/lib/logger";

const prisma = new PrismaClient();

// Configuration constants
const MARKET_OPEN_HOUR = 9;
const MARKET_OPEN_MINUTE = 30;
const PREDICTION_WINDOW_HOURS = 4;

export const PredictionEventRepo = {
    getPredictionEvents,
    getPredictionEventById,
    submitUserPrediction,
    getUserEventPrediction,
    setPredictionEventEndValue,
    calculateEventWinner,
    getPredictionEventByDate,
    getTodayPredictionEvent,
    getYesterdayPredictionEvent,
    getLatestUpcomingPredictionEvent,
    getPredictionEventWinners,
    isPredictionEventAvailable,
};

async function getPredictionEvents(userId?: string): Promise<EventWithPrediction[]> {
    try {
        const events = await prisma.dowJonesPredictionEvent.findMany({
            orderBy: { eventDate: "asc" },
            include: {
                predictions: {
                    where: {
                        OR: [
                            {
                                event: {
                                    isClosestUserCalculated: true,
                                },
                            },
                            ...(userId ? [{ userId: userId }] : []),
                        ],
                    },
                    select: {
                        userId: true,
                        prediction: true,
                        isWinner: true,
                        user: {
                            select: {
                                id: true,
                                name: true,
                                firstName: true,
                                lastName: true,
                                picture: true,
                            },
                        },
                    },
                    orderBy: {
                        prediction: "asc",
                    },
                },
            },
        });

        return events;

        // // Convert eventDate to US time zone
        // return events.map(event => ({
        //     ...event,
        //     eventDate: toZonedTime(event.eventDate, COMPETITION_TIMEZONE),
        // }));
    } catch (error) {
        console.error("Error in getPredictionEvents:", error);
        throw new AppError("Failed to fetch prediction events");
    }
}

async function getPredictionEventById( eventId: string): Promise<DowJonesPredictionEvent | null> {
    try {
        return await prisma.dowJonesPredictionEvent.findFirst({
            where: { id: eventId },
        });
    } catch (error) {
        console.error("Error in getPredictionEventById:", error);
        throw new AppError("Failed to fetch prediction event");
    }
}

async function submitUserPrediction( userId: string, eventId: string, predictionValue: number): Promise<UserDowJonesPrediction> {
    try {
        // TODO - Testing only, uncomment out for production
        // const isAvailable = await PredictionEventRepo.isPredictionEventAvailable();
        // if (!isAvailable) {
        //     throw new AppError("Prediction event is not currently available");
        // }

        const validatedData = UserPredictionSchema.parse({
            userId,
            eventId,
            predictionValue,
        });

        const existingEvent = getPredictionEventById(validatedData.eventId);

        if (!existingEvent) {
            throw new AppError(
                "Prediction Event not exists"
            );
        }

        const existingPrediction =
            await prisma.userDowJonesPrediction.findUnique({
                where: {
                    userId_eventId: {
                        userId: validatedData.userId,
                        eventId: validatedData.eventId,
                    },
                },
            });

        if (existingPrediction) {
            throw new Error(
                "User has already submitted a prediction for this event"
            );
        }

        return await prisma.userDowJonesPrediction.create({
            data: {
                userId: validatedData.userId,
                eventId: validatedData.eventId,
                prediction: validatedData.predictionValue,
            },
        });
    } catch (error) {
        console.error("Error in submitUserPrediction:", error);
        if (error instanceof z.ZodError) {
            throw new AppError("Invalid input data", { details: error.errors });
        }
        throw error;
    }
}

async function getUserEventPrediction( userId: string, eventId: string): Promise<UserDowJonesPrediction | null> {
    try {
        return await prisma.userDowJonesPrediction.findUnique({
            where: {
                userId_eventId: {
                    userId,
                    eventId,
                },
            },
        });
    } catch (error) {
        console.error("Error in getUserEventPrediction:", error);
        throw new AppError("Failed to fetch user prediction");
    }
}

async function setPredictionEventEndValue( eventId: string, actualValue: number): Promise<DowJonesPredictionEvent> {
    try {
        return await prisma.dowJonesPredictionEvent.update({
            where: { id: eventId },
            data: {
                actualValue,
                isClosestUserCalculated: false, // Reset this flag as we're setting a new actual value
            },
        });
    } catch (error) {
        console.error("Error in setPredictionEventEndValue:", error);
        throw new AppError("Failed to set prediction event end value");
    }
}

async function calculateEventWinner(eventId: string): Promise<UserDowJonesPrediction | null> {
    try {
        const event = await prisma.dowJonesPredictionEvent.findUnique({
            where: { id: eventId },
            include: { predictions: true },
        });

        if (!event) {
            throw new AppError("Event not found");
        }

        if (event.actualValue === null) {
            throw new AppError("Actual value not set");
        }

        if (event.predictions.length === 0) {
            return null;
        }

        // Sort predictions by difference from actual value and submission time
        const sortedPredictions = event.predictions.sort((a, b) => {
            const diffA = Math.abs(a.prediction - event.actualValue!);
            const diffB = Math.abs(b.prediction - event.actualValue!);
            // If differences are equal, earlier submission wins
            if (diffA === diffB) {
                return a.createdAt.getTime() - b.createdAt.getTime();
            }
            return diffA - diffB;
        });

        logger.info(`[calculateEventWinner details] received predictions: ${sortedPredictions.length}`);

        // Take only the first prediction (closest or earliest in case of tie)
        const winner = sortedPredictions[0];

        // Wrap database updates in a transaction
        const updatedWinner = await prisma.$transaction(async (tx) => {
            // Reset all predictions to non-winner
            await tx.userDowJonesPrediction.updateMany({
                where: { eventId },
                data: { isWinner: false },
            });

            // Set the winner
            const updatedPrediction = await tx.userDowJonesPrediction.update({
                where: { id: winner.id },
                data: { isWinner: true },
            });

            // Update event status
            await tx.dowJonesPredictionEvent.update({
                where: { id: eventId },
                data: { isClosestUserCalculated: true },
            });

            return updatedPrediction;
        });

        return updatedWinner;
    } catch (error) {
        console.error("Error in calculateEventWinners:", error);
        throw new Error("Failed to calculate event winner");
    }
}

async function getPredictionEventByDate(givenDate: Date): Promise<DowJonesPredictionEvent | null> {
    try {
        const date = new Date(givenDate);
        const startOfDay = new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            0, 0, 0
        );
        
        const endOfDay = new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23, 59, 59, 999
        );

        return await prisma.dowJonesPredictionEvent.findFirst({
            where: {
                eventDate: {
                    gte: startOfDay,
                    lte: endOfDay,
                },
            },
            orderBy: {
                eventDate: 'asc',
            },
        });
    } catch (error) {
        console.error("Error in getPredictionEventByDate:", error);
        throw new AppError("Failed to fetch prediction event for specified date");
    }
}

async function getTodayPredictionEvent(): Promise<DowJonesPredictionEvent | null> {
    try {
        const today = new Date();
        const startOfDay = new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate(),
            0, 0, 0
        );
        const endOfDay = new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate(),
            23, 59, 59, 999
        );

        console.log(startOfDay, endOfDay);

        return await prisma.dowJonesPredictionEvent.findFirst({
            where: {
                eventDate: {
                    gte: startOfDay,
                    lte: endOfDay,
                },
            },
            orderBy: {
                eventDate: 'asc',
            },
        });
    } catch (error) {
        console.error("Error in getTodayPredictionEvent:", error);
        throw new AppError("Failed to fetch current prediction event");
    }
}

async function getYesterdayPredictionEvent(): Promise<DowJonesPredictionEvent | null> {
    try {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        
        const startOfYesterday = new Date(
            yesterday.getFullYear(),
            yesterday.getMonth(),
            yesterday.getDate(),
            0, 0, 0
        );

        const endOfYesterday = new Date(
            yesterday.getFullYear(),
            yesterday.getMonth(),
            yesterday.getDate(),
            23, 59, 59, 999
        );

        return await prisma.dowJonesPredictionEvent.findFirst({
            where: {
                eventDate: {
                    gte: startOfYesterday,
                    lte: endOfYesterday,
                },
            },
            orderBy: {
                eventDate: 'desc',
            },
        });
    } catch (error) {
        console.error("Error in getYesterdayPredictionEvent:", error);
        throw new AppError("Failed to fetch yesterday's prediction event");
    }
}

async function getLatestUpcomingPredictionEvent(): Promise<DowJonesPredictionEvent | null> {
    try {
        return await prisma.dowJonesPredictionEvent.findFirst({
            where: {
                eventDate: {
                    gt: new Date(),
                },
            },
            orderBy: { eventDate: 'asc' },
        });
    } catch (error) {
        console.error("Error in getLatestUpcomingPredictionEvent:", error);
        throw new AppError("Failed to fetch latest upcoming prediction event");
    }
}

async function getPredictionEventWinners(eventId: string): Promise<UserForPublic[]> {
    try {
        const winningPredictions = await prisma.userDowJonesPrediction.findMany(
            {
                where: {
                    eventId: eventId,
                    isWinner: true,
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            firstName: true,
                            lastName: true,
                            picture: true,
                        },
                    },
                },
            }
        );

        return winningPredictions.map((prediction) => prediction.user as UserForPublic);
    } catch (error) {
        console.error("Error in getPredictionEventWinner:", error);
        throw new AppError("Failed to fetch prediction event winner");
    }
}

async function isPredictionEventAvailable(): Promise<boolean> {
    try {
        const nowInNewYork = getOffsetUTCDate(COMPETITION_UTC_OFFSET);
        const currentDay = nowInNewYork.getDay();

        // Check if it's a weekday (Monday to Friday)
        if (currentDay >= 1 && currentDay <= 5) {
            const marketOpenTime = new Date(nowInNewYork);
            marketOpenTime.setHours(MARKET_OPEN_HOUR, MARKET_OPEN_MINUTE, 0, 0);

            const predictionEndTime = new Date(marketOpenTime);
            predictionEndTime.setHours(marketOpenTime.getHours() + PREDICTION_WINDOW_HOURS);

            return nowInNewYork >= marketOpenTime && nowInNewYork <= predictionEndTime;
        }

        return false;
    } catch (error) {
        console.error("Error in isPredictionEventAvailable:", error);
        throw new AppError("Failed to check prediction event availability");
    }
}
