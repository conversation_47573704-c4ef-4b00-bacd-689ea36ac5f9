import { prisma } from "@/lib/prisma";
import { logError, AppError } from "@/lib/error-handler";
import { CompetitionRepo } from "./competition";
import { VIRTUAL_TRADING_COMPETITION_PORTFOLIO_ID } from "@/config/constants";
import { PositionOperation } from "@/types/portfolio";
import TransactionRepo from "./transaction";
import { TransactionType } from "@prisma/client";
import { aiDatabaseApi } from "@/lib/api/ai-database-api-client";
import { StocksPriceResponse } from "@/types/api/ai-database";
import { MissionType } from "@/types/api/zenta";
import { apiPath } from "@/config/api";
import { AppErrorWithTranslation } from "@/lib/errors/error-with-translation";

export const PositionRepo = {
    getPositions,
    createPosition,
    updatePosition,
    isLessThanMaxPositionCount,
};

async function getPositions(portfolioId: number, userId: string) {
    try {
        if (+portfolioId === VIRTUAL_TRADING_COMPETITION_PORTFOLIO_ID) {
            const portfolio = await CompetitionRepo.getUserCompetitionPortfolio(userId);
            if (!portfolio) {
                throw new AppErrorWithTranslation({
                    message: "Portfolio not found",
                    translationKey: "Errors.HttpStatus.NotFound",
                });
            }
            portfolioId = portfolio.id;
        }

        const positions = await prisma.position.findMany({
            where: { portfolioId },
        });

        return positions;
    } catch (error) {
        logError(error, { operation: "getPositions", userId });
        throw new AppErrorWithTranslation({
            message: "Failed to get positions",
            translationKey: "Errors.HttpStatus.NotFound",
        });
    }
}

async function createPosition(portfolioId: number, userId: string, symbol: string, shares: number, price: number) {
    try {
        const totalCost = shares * price;

        if (+portfolioId === VIRTUAL_TRADING_COMPETITION_PORTFOLIO_ID) {
            const portfolio = await CompetitionRepo.getUserCompetitionPortfolio(userId);
            if (!portfolio) {
                throw new AppErrorWithTranslation({
                    message: "Portfolio not found",
                    translationKey: "Errors.HttpStatus.NotFound",
                });
            }
            portfolioId = portfolio.id;

            if (totalCost > portfolio.cashBalance) {
                throw new AppErrorWithTranslation({
                    message: "Insufficient funds",
                    translationKey: "Portfolio.Errors.InsufficientFunds",
                });
            }
        }

        return await prisma.$transaction(async (prisma) => {
            // Check if position already exists
            const existingPosition = await prisma.position.findFirst({
                where: { portfolioId, symbol },
            });

            let position;
            if (existingPosition) {
                // Update existing position
                const newTotalShares = existingPosition.shares + shares;
                const newAveragePrice = ((existingPosition.shares * existingPosition.averagePrice) + totalCost) / newTotalShares;

                position = await prisma.position.update({
                    where: { id: existingPosition.id },
                    data: {
                        shares: newTotalShares,
                        averagePrice: newAveragePrice,
                    },
                });
            } else {
                // Create new position
                position = await prisma.position.create({
                    data: {
                        portfolioId,
                        symbol,
                        shares,
                        averagePrice: price,
                    },
                });
            }

            // Update portfolio
            const updatedPortfolio = await prisma.portfolio.update({
                where: { id: portfolioId },
                data: {
                    cashBalance: { decrement: totalCost },
                    investedValue: { increment: totalCost },
                },
            });

            // Create transaction
            await prisma.transaction.create({
                data: {
                    portfolioId,
                    positionId: position.id,
                    symbol,
                    type: TransactionType.BUY,
                    shares,
                    price,
                },
            });

            return position;
        });
    } catch (error) {
        logError(error, { operation: "createPosition", userId, symbol });
        throw new AppErrorWithTranslation({
            message: "Failed to create position",
            translationKey: "Errors.HttpStatus.InternalServerError",
        });
    }
}

async function updatePosition(userId: string, positionId: number, operation: PositionOperation, shares: number) {
    try {
        const position = await prisma.position.findUnique({
            where: { id: positionId },
            include: { portfolio: true },
        });

        if (!position) {
            throw new AppErrorWithTranslation({
                message: "Position not found",
                translationKey: "Errors.HttpStatus.NotFound",
            });
        }
        if (position.portfolio.userId !== userId) {
            throw new AppErrorWithTranslation({
                message: "Unauthorized",
                translationKey: "Errors.HttpStatus.Unauthorized",
            });
        }

        const transactionType = operation === PositionOperation.BUY ? TransactionType.BUY : TransactionType.SELL;

        // const price = (await globalStockData.getStock(position.symbol))?.lastPrice;
        // if (!price) throw new AppError("Failed to get latest price");
        let price = -1;
        try {
            const response: StocksPriceResponse = await aiDatabaseApi.post(`${apiPath.server.AI_DATABASE.stocksPrice}`, {
                mission_type: MissionType.stocksPrice,
                symbol_list: [position.symbol],
            });

            if (!response || !response.response) {
                throw new Error(`Failed to fetch stock quotes for ${position.symbol}`);
            }

            const data = response.response;

            const quote = data[position.symbol];
            price = quote || -1;
        } catch (error) {
            console.error(`Error fetching stock data for ${position.symbol}:`);
        }

        if (price === -1) throw new Error(`Failed to get latest price for ${position.symbol}`);

        const totalCost = shares * price;

        let updatedShares = position.shares;
        let updatedAveragePrice = position.averagePrice;

        if (operation === PositionOperation.BUY) {
            if (totalCost > position.portfolio.cashBalance) {
                throw new AppErrorWithTranslation({
                    message: "Insufficient funds",
                    translationKey: "Portfolio.Errors.InsufficientFunds",
                });
            }

            updatedShares += shares;
            updatedAveragePrice = ((position.shares * position.averagePrice) + totalCost) / updatedShares;

            await prisma.portfolio.update({
                where: { id: position.portfolioId },
                data: {
                    cashBalance: { decrement: totalCost },
                    investedValue: { increment: totalCost },
                },
            });
        } else {
            if (shares > position.shares) {
                throw new AppErrorWithTranslation({
                    message: "Insufficient shares to sell",
                    translationKey: "Portfolio.Errors.InsufficientSharesToSell",
                });
            }
            updatedShares -= shares;

            const saleProceeds = shares * price;
            const costBasis = shares * position.averagePrice;
            const realizedGain = saleProceeds - costBasis;

            await prisma.portfolio.update({
                where: { id: position.portfolioId },
                data: {
                    cashBalance: { increment: saleProceeds },
                    investedValue: { decrement: costBasis },
                    realizedGain: { increment: realizedGain },
                },
            });
        }

        const updatedPosition = await prisma.position.update({
            where: { id: positionId },
            data: { 
                shares: updatedShares,
                averagePrice: updatedAveragePrice,
            },
        });

        await TransactionRepo.createTransaction(
            position.portfolioId,
            positionId,
            position.symbol,
            transactionType,
            shares,
            price
        );

        return updatedPosition;
    } catch (error) {
        logError(error, { operation: "updatePosition", userId, positionId });
        throw new Error("Failed to update position");
    }
}

export const MAX_POSITION_COUNT = 10;
async function isLessThanMaxPositionCount(portfolioId: number, userId: string) {
    const positions = await getPositions(portfolioId, userId);
    return positions.filter(position => position.shares > 0).length < MAX_POSITION_COUNT;
}

export default PositionRepo;