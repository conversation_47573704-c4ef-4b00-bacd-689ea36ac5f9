import { PrismaClient, Portfolio } from '@prisma/client';
import PortfolioRepo from '../../repository/portfolio';
import logger from '../../lib/logger';

const prisma = new PrismaClient();

export async function createPortfolioSnapshot(portfolios: Portfolio[]) {
  try {
    // Create daily snapshots for each portfolio
    let createdSnapshotCount = 0;
    let skippedPortfolioCount = 0;
    
    for (const portfolio of portfolios) {
      const snapshot = await PortfolioRepo.createDailySnapshot(portfolio.id);
      if (snapshot) {
        createdSnapshotCount++;
      } else {
        skippedPortfolioCount++;
      }
    }

    logger.info(`[Job details] Created (${createdSnapshotCount}) snapshots and skipped (${skippedPortfolioCount}) portfolios.`);
  } catch (error) {
    logger.error('Error in createPortfolioSnapshot:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}
