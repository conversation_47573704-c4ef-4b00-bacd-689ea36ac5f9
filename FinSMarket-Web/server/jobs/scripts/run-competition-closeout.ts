import { scheduleJobsQueue } from "@/lib/queue";
import { JobNames } from "..";
import readline from 'readline';
import { CompetitionRepo } from "@/repository/competition";

async function promptUser(question: string): Promise<boolean> {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y');
        });
    });
}

async function main() {
    const date = new Date();
   
    const competitionStatus = await CompetitionRepo.getCompetitionStatus(date);

    console.log(`\nCurrent Date: ${date.toLocaleString()}`);
    console.log(`Competition End Date: ${competitionStatus?.endDate?.toLocaleString()}`);

    const shouldContinue = await promptUser('\nDo you want to continue? (Y/N): ');
    
    if (!shouldContinue) {
        console.log('\nOperation cancelled by user');
        return;
    }

    console.log("Closing competition portfolios...");

    await scheduleJobsQueue.add(JobNames.CloseCompetitionPortfolios, {});
    console.log(`Added competition close out job to the queue for date: ${date}`);
}

main()
    .then(() => {
    })
    .catch((error) => {
        console.error("Error adding competition jobs to the queue: ", error);
    })
    .finally(() => {
        process.exit(0);
    });
