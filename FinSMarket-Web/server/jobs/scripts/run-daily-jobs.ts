import { scheduleJobsQueue } from "@/lib/queue";
import { JobNames } from "..";


async function main() {
    await scheduleJobsQueue.add(JobNames.DailyJobs, {});
    console.log("Added daily jobs to the queue");
}

main()
    .then(() => {
        console.log("Daily jobs added to the queue");
    })
    .catch((error) => {
        console.error("Error adding daily jobs to the queue: ", error);
    })
    .finally(() => {
        process.exit(0);
    });
