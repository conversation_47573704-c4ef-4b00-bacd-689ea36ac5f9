import { scheduleJobsQueue } from "@/lib/queue";
import { JobNames } from "..";

async function main() {
    // Process Date is local time, will be converted to competition timezone in the worker jobs
    const processDate = process.argv[2] 
        ? new Date(process.argv[2])
        : new Date();

    await scheduleJobsQueue.add(JobNames.CompetitionJobs, { 
        ignoreMarketTime: true, 
        processDate 
    });
    console.log(`Added competition jobs to the queue for date: ${processDate}`);
}

main()
    .then(() => {
        console.log("Competition jobs added to the queue");
    })
    .catch((error) => {
        console.error("Error adding competition jobs to the queue: ", error);
    })
    .finally(() => {
        process.exit(0);
    });
