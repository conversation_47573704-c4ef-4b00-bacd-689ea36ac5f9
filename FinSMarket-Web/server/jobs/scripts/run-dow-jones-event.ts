import { scheduleJobsQueue } from "@/lib/queue";
import { JobNames } from "..";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";

async function main() {
    const argv = await yargs(hideBin(process.argv))
        .option('date', {
            alias: 'd',
            type: 'string',
            description: 'Process date (YYYY-MM-DD)',
            default: new Date().toISOString().split('T')[0]
        })
        .option('price', {
            alias: 'p',
            type: 'number',
            description: 'Dow Jones price',
            // demandOption: true
        })
        .help()
        .argv;

    const processDate = new Date(argv.date);

    // For testing
    // const result = await processDowJonesPredictionEvent(processDate, argv.price);
    
    await scheduleJobsQueue.add(JobNames.ProcessDowJonesPredictionEvent, {
        processDate,
        price: argv.price
    });

    console.log(`Added Dow Jones event to the queue for date: ${processDate}, price: ${argv.price}`);
}

main()
    .catch((error) => {
        console.error("Error adding Dow Jones event to the queue:", error);
    })
    .finally(() => {
        process.exit(0);
    }); 