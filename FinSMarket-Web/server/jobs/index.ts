// TODO
/*
    Daily Jobs:
    - Fetch Stocks List
    - Fetch Stock Data (Price, Dividend, etc.)
    - Update User Portfolio (Positions changes, returns, etc.)

    Competition Jobs:
    - Fetch Stocks Closing Prices
    - Update Competition Portfolio (Positions changes, returns, etc.)
    - Calculate Leaderboard (Daily, Weekly, Monthly)
*/

export const JobNames = {
    // Jobs
    DailyJobs: 'daily-jobs',
    CompetitionJobs: 'competition-jobs',
    // Actions
    FetchStockListWithStockPrice: 'fetch-stock-list-with-stock-price',
    CalculatePortfolioChanges: 'calculate-portfolio-changes',
    CreatePortfolioSnapshot: 'create-portfolio-snapshot',
    CalculateCompetitionRankings: 'calculate-competition-rankings',
    ProcessDowJonesPredictionEvent: 'process-dow-jones-prediction-event',
    CloseCompetitionPortfolios: 'close-competition-portfolios',
    TestLog: 'test-log',
} as const;
