import { Portfolio, PrismaClient, RankingInterval } from "@prisma/client";
import { CompetitionRepo } from "../../repository/competition";
import logger from "../../lib/logger";
import PortfolioRepo from "@/repository/portfolio";
import { fromZonedTime, toZonedTime } from "date-fns-tz";
import {
    COMPETITION_TIMEZONE,
    isHoliday,
    isTodayMarketEnd,
} from "@/utils/stock-market";
import { isWeekend, set } from "date-fns";
import { CalculatePortfolioReturns } from "@/types/competition";

const prisma = new PrismaClient();

// TODO - Need to test
export async function calculateCompetitionRankings(portfolios: Portfolio[], date: Date = new Date()) {
    try {
        let targetDate = new Date(date);
        // Convert input date to NY timezone first
        // let targetDate = toZonedTime(date, COMPETITION_TIMEZONE);

        const day = targetDate.getDay();
        if (isTodayMarketEnd(fromZonedTime(targetDate, COMPETITION_TIMEZONE)) && !isWeekend(targetDate)) {
            // If given date is market close, use given date
            logger.info(`[calculateCompetitionRankings - determine date] Market is closed, use given date: ${targetDate.toISOString()} (${COMPETITION_TIMEZONE})`);
        }
        else {
            if (day === 0 || day === 6 || day === 1) {
                // If weekend or Monday before market close, find last Friday
                const daysToSubtract = day === 0 ? 2 : (day === 1 ? 3 : 1); // Sunday -> -2, Monday -> -3, Saturday -> -1
                targetDate = new Date(targetDate.setDate(targetDate.getDate() - daysToSubtract));
                while (isHoliday(targetDate)) {
                    targetDate = new Date(targetDate.setDate(targetDate.getDate() - 1));
                }
                logger.info(`[calculateCompetitionRankings - determine date] Found last Friday: ${targetDate.toISOString()} (${COMPETITION_TIMEZONE})`);
            } else {
                // Tuesday-Friday before market close - use previous day
                targetDate = new Date(targetDate.setDate(targetDate.getDate() - 1));
                logger.info(`[calculateCompetitionRankings - determine date] Found previous day: ${targetDate.toISOString()} (${COMPETITION_TIMEZONE})`);
            }
        }

        targetDate = set(targetDate, { hours: 23, minutes: 59, seconds: 59, milliseconds: 999 })
        // targetDate = fromZonedTime(targetDate, COMPETITION_TIMEZONE);
        // targetDate = set(targetDate, { hours: 16, minutes: 31, seconds: 0, milliseconds: 0 })
        logger.info(`[calculateCompetitionRankings - determine date] Final date: ${targetDate.toISOString()} (${COMPETITION_TIMEZONE})`);

        const dailyStats = await calculateRankings(
            portfolios,
            RankingInterval.DAILY,
            targetDate
        );
        const weeklyStats = await calculateRankings(
            portfolios,
            RankingInterval.WEEKLY,
            targetDate
        );
        const monthlyStats = await calculateRankings(
            portfolios,
            RankingInterval.MONTHLY,
            targetDate
        );

        logger.info(
            `[Job details] Competition rankings calculation completed: ${JSON.stringify({
                date: targetDate,
                daily: dailyStats,
                weekly: weeklyStats,
                monthly: monthlyStats,
            }, null, 2)}`
        );
    } catch (error) {
        logger.error("Error in calculateCompetitionRankings:", error);
        // logger.error("Error in calculateCompetitionRankings:", JSON.stringify(error, null, 2));
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

export async function calculateRankings(
    portfolios: Portfolio[],
    interval: RankingInterval,
    currentDate: Date
) {
    let { startDate, endDate } = CompetitionRepo.getDateRangeByInterval(
        interval,
        currentDate
    );

    startDate = fromZonedTime(startDate, COMPETITION_TIMEZONE);
    endDate = fromZonedTime(endDate, COMPETITION_TIMEZONE);

    const portfolioReturns: CalculatePortfolioReturns = new Map<string, [number, number]>();
    const stats = {
        range: {
            startDate,
            endDate,
        },
        processed: 0,
        skipped: 0,
        updated: 0,
        errors: 0,
    };

    for (const portfolio of portfolios) {
        try {
            const calculatedPortfolioReturns =
                await PortfolioRepo.calculatePortfolioReturns(
                    portfolio.id,
                    startDate,
                    endDate
                );
            if (!calculatedPortfolioReturns) {
                stats.skipped++;
                continue;
            }
            portfolioReturns.set(portfolio.id.toString(), [calculatedPortfolioReturns.overallReturnRate, calculatedPortfolioReturns.overallReturn]);
            stats.processed++;
        } catch (error) {
            logger.error(
                `Error processing portfolio ${portfolio.id} for ${interval} ranking:`,
                error
            );
            stats.errors++;
            continue;
        }
    }

    // console.log("portfolioReturns: ", portfolioReturns);

    const sortedReturns = Array.from(portfolioReturns.entries())
        .sort((a, b) => b[1][0] - a[1][0]) // Index 0 is returnRate, index 1 is returnAmount
        .map(([portfolioId, [returnRate, returns]], index) => ({
            portfolioId: parseInt(portfolioId),
            returnRate,
            returns,
            rank: index + 1,
        }));

    // console.log("sortedReturns: ", sortedReturns);

    await CompetitionRepo.updateRankings(sortedReturns, interval, endDate);
    stats.updated = sortedReturns.length;
    // logger.info(`${interval} ranking calculation completed:`, {
    //     totalPortfolios: portfolios.length,
    //     ...stats,
    //     timestamp: endDate
    // });

    return stats;
}
