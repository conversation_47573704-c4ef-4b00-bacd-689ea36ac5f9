import StockRepo from '@/repository/stock';
import { PrismaClient } from '@prisma/client';
import { stockApi } from "@/lib/stock-api-client";
import { apiPath } from '@/config/api';
import { FMPStockListItem } from '@/types/api/stocks';
import logger from '@/lib/logger';

const prisma = new PrismaClient();

export async function fetchStockListWithStockPrice() {
  try {
    const result = await stockApi.get(apiPath.server.FMP.stock_list);
    const stocks = result as unknown as FMPStockListItem[];
    
    const [createdCount, updatedCount] = await StockRepo.createOrUpdateStocks(stocks, true);

    logger.info(`[Job details] Created (${createdCount}) stocks and updated (${updatedCount}) stocks.`);
  } catch (error) {
    logger.error('Error in updateStockList:', { error });
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}