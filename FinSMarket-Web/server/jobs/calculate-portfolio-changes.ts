import { PrismaClient, Portfolio } from '@prisma/client';
import PortfolioRepo from '../../repository/portfolio';
import logger from '../../lib/logger';

const prisma = new PrismaClient();

export async function calculatePortfolioChanges(portfolios: Portfolio[]) {
  try {
    // Update portfolios with latest prices
    let updatedPortfolioCount = 0;

    for (const portfolio of portfolios) {
      const updatedPortfolio = await PortfolioRepo.updatePortfolioWithLatestPrices(portfolio.id);
      // logger.info(`Portfolio ${updatedPortfolio.id} updated. New invested value: ${updatedPortfolio.investedValue}, Unrealized gain: ${updatedPortfolio.unrealizedGain}`);
      updatedPortfolioCount++;
    }

    logger.info(`[Job details] Updated (${updatedPortfolioCount}) portfolios.`);
  } catch (error) {
    logger.error('Error in calculatePortfolioChanges:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}
