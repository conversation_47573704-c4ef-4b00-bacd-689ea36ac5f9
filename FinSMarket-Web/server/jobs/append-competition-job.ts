import { PrismaClient } from "@prisma/client";
import CompetitionRepo from "@/repository/competition";
import { scheduleJobsQueue } from "@/lib/queue";
import { JobNames } from ".";

const prisma = new PrismaClient();

export async function appendCompetitionJob() {
    try {
        console.log("Appending competition job...\n\n");

		const isCompetitionActive = await CompetitionRepo.isAnyCompetitionActive();
		if (!isCompetitionActive) {
			console.log("No active competition found.");
			return;
		}
		scheduleJobsQueue.add(JobNames.CompetitionJobs, {});

        console.log("Competition jobs appended successfully.");
    } catch (error) {
        console.error("Error in appendCompetitionJob:", error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}
