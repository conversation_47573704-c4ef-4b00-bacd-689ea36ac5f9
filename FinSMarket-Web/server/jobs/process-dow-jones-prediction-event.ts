import { DowJonesPredictionEvent, PrismaClient } from '@prisma/client';
import { PredictionEventRepo } from '@/repository/prediction-event';
import logger from '@/lib/logger';
import { apiPath } from '@/config/api';
import { aiDatabaseApi } from '@/lib/api/ai-database-api-client';
import { MissionType } from '@/types/api/zenta';
import { StocksPriceResponse } from '@/types/api/ai-database';

const prisma = new PrismaClient();

const DOW_JONES_SYMBOL = '^DJI';

export async function processDowJonesPredictionEvent(processDate?: Date, price?: number) {
  try {
    // const todayEvent = await PredictionEventRepo.getLatestUpcomingPredictionEvent();
    let todayEvent: DowJonesPredictionEvent | null = null;
    if (processDate) {
      todayEvent = await PredictionEventRepo.getPredictionEventByDate(processDate);
    } else {
      todayEvent = await PredictionEventRepo.getYesterdayPredictionEvent();
    }

    if (!todayEvent) {
      logger.info('[processDowJonesPredictionWinners] No Dow Jones prediction event for today. Skipping processing.');
      return;
    }

    logger.info(`[processDowJonesPredictionWinners] Dow Jones prediction event ${JSON.stringify(todayEvent, null, 2)}`);

    if (todayEvent.isClosestUserCalculated) {
      logger.info(`[processDowJonesPredictionWinners] Dow Jones prediction event ${todayEvent.id} already processed. Skipping.`);
      return;
    }

    let actualValue: number | undefined | null = price;

    if (!actualValue) {
      const response: StocksPriceResponse = await aiDatabaseApi.post(`${apiPath.server.AI_DATABASE.stocksPrice}`, {
          mission_type: MissionType.stocksPrice,
          symbol_list: [DOW_JONES_SYMBOL],
      });

      if (!response || !response.response) {
          throw new Error(`Failed to fetch stock quotes for DJI`);
    }

      const data = response.response;
      actualValue = data[DOW_JONES_SYMBOL];
    }

    if (!actualValue) {
      throw new Error(`Failed to fetch stock quotes for DJI`);
    }

    // Set the actual value for the event
    await PredictionEventRepo.setPredictionEventEndValue(todayEvent.id, actualValue);

    // Calculate the winners
    const winner = await PredictionEventRepo.calculateEventWinner(todayEvent.id);

    if (!winner) {
      logger.info(`[processDowJonesPredictionWinners] No predictions found for event: [${todayEvent.id}] ${todayEvent.name}`);
      return;
    }

    logger.info(`[processDowJonesPredictionWinners] Dow Jones prediction event [${todayEvent.id}] ${todayEvent.name} processed. Winner: ${winner.userId}, Winner prediction: ${winner.prediction}`);


  } catch (error) {
    logger.error('[processDowJonesPredictionWinners] Error:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}
