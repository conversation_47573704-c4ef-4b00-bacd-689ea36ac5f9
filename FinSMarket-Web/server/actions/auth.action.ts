'use server'

import { z } from "zod"
import { lucia } from "@/lib/lucia"
import { prisma } from "@/lib/prisma"
import { googleOAuthClient, appleOAuthClient } from "@/lib/oauth"
import { Argon2id } from 'oslo/password'
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { generateCodeVerifier, generateState } from "arctic"
import { SignInFormSchema, SignUpFormSchema } from '@/types/validations/auth';
import { SyncService } from "@/services/sync-service"
import { trySilently } from "@/lib/utils"

export const signUp = async (values: z.infer<typeof SignUpFormSchema>) => {
    try {
        const existingUser = await prisma.user.findUnique({
            where: {
                email: values.email.toLowerCase()
            }
        })

        const hashedPassword = await new Argon2id().hash(values.password)

        let user;
        if (existingUser && !existingUser.verifiedAt) {
            // Update existing unverified user
            user = await prisma.user.update({
                where: { id: existingUser.id },
                data: {
                    firstName: values.firstName,
                    lastName: values.lastName,
                    hashedPassword
                }
            })

            // Remove all email verification tokens for this user
            await prisma.emailVerificationToken.deleteMany({
                where: { userId: existingUser.id }
            })
        } else if (!existingUser) {
            // Create new user
            user = await prisma.user.create({
                data: {
                    email: values.email.toLowerCase(),
                    firstName: values.firstName,
                    lastName: values.lastName,
                    hashedPassword
                }
            });

            trySilently(async () => await SyncService.syncUser(values.email.toLowerCase()));
        } else {
            // User exists and is verified
            return { error: "Auth.message.signUp.userExists.title", success: false }
        }
        const session = await lucia.createSession(user.id, {})
        const sessionCookie = await lucia.createSessionCookie(session.id)
        cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes)
        return { success: true }
    } catch (error) {
        return { error: 'Common.message.somethingWentWrong', success: false }
    }
}

export const signIn = async (values: z.infer<typeof SignInFormSchema>) => {
    const user = await prisma.user.findUnique({
        where: {
            email: values.email.toLowerCase()
        }
    })
    if (!user || !user.hashedPassword) {
        return { success: false, error: "Auth.message.incorrectCredential" }
    }
    const passwordMatch = await new Argon2id().verify(user.hashedPassword, values.password)
    if (!passwordMatch) {
        return { success: false, error: "Auth.message.incorrectCredential" }
    }
    // successfully login
    const session = await lucia.createSession(user.id, {})
    const sessionCookie = await lucia.createSessionCookie(session.id)
    cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes)
    return { success: true }
}

export const logOut = async () => {
    const sessionCookie = await lucia.createBlankSessionCookie()
    cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes)
    return redirect('/')
}

export const getGoogleOauthConsentUrl = async (callback?: string | null) => {
    try {
        const state = generateState()
        const codeVerifier = generateCodeVerifier()

        cookies().set('codeVerifier', codeVerifier, {
            httpOnly: true,
            domain: process.env.NEXT_PUBLIC_DOMAIN || undefined,
            secure: process.env.NEXT_PUBLIC_TARGET_ENV === 'production',
        })
        
        cookies().set('state', state, {
            httpOnly: true,
            domain: process.env.NEXT_PUBLIC_DOMAIN || undefined,
            secure: process.env.NEXT_PUBLIC_TARGET_ENV === 'production',
        })

        // Store callback URL in cookie if provided
        if (callback) {
            cookies().set('oauth_callback', callback, {
                httpOnly: true,
                domain: process.env.NEXT_PUBLIC_DOMAIN || undefined,
                secure: process.env.NEXT_PUBLIC_TARGET_ENV === 'production',
            })
        }

        const authUrl = await googleOAuthClient.createAuthorizationURL(state, codeVerifier, ['email', 'profile'])
        return { success: true, url: authUrl.toString() }

    } catch (error) {
        return { success: false, error: "Common.message.somethingWentWrong" }
    }
}

export const getAppleOauthConsentUrl = async (callback?: string | null) => {
    try {
        const state = generateState()
        const codeVerifier = generateCodeVerifier()

        cookies().set('codeVerifier', codeVerifier, {
            httpOnly: true,
            domain: process.env.NEXT_PUBLIC_DOMAIN || undefined,
            secure: process.env.NEXT_PUBLIC_TARGET_ENV === 'production',
        })
        
        cookies().set('state', state, {
            httpOnly: true,
            domain: process.env.NEXT_PUBLIC_DOMAIN || undefined,
            secure: process.env.NEXT_PUBLIC_TARGET_ENV === 'production',
        })

        if (callback) {
            cookies().set('oauth_callback', callback, {
                httpOnly: true,
                domain: process.env.NEXT_PUBLIC_DOMAIN || undefined,
                secure: process.env.NEXT_PUBLIC_TARGET_ENV === 'production',
            })
        }

        const authUrl = await appleOAuthClient.createAuthorizationURL(
            state, 
            // codeVerifier,
            ['name', 'email']
        )
        authUrl.searchParams.set("response_mode", "form_post");

        return { success: true, url: authUrl.toString() }

    } catch (error) {
        return { success: false, error: "Common.message.somethingWentWrong" }
    }
}

export const deleteAccount = async (userId: string) => {
    try {
        if (!userId) {
            return { success: false, error: "Auth.message.unauthorized" };
        }

        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { email: true }
        });

        if (!user) {
            return { success: false, error: "Auth.message.userNotFound" };
        }

        const deletedAt = new Date();
        const anonymizedEmail = `deleted;${user.email.toLowerCase()};${deletedAt.toISOString()}`;

        // Start a transaction to ensure all operations succeed or fail together
        await prisma.$transaction(async (tx) => {
            // Soft delete and anonymize user
            await tx.user.update({
                where: { id: userId },
                data: {
                    deletedAt,
                    email: anonymizedEmail,
                    firstName: null,
                    lastName: null,
                    name: null,
                    hashedPassword: null,
                    picture: null,
                }
            });

            // Soft delete UserStockView records
            await tx.userStockView.updateMany({
                where: { userId },
                data: { deletedAt }
            });

            // Soft delete UserSurveyResponse records
            await tx.userSurveyResponse.updateMany({
                where: { userId },
                data: { deletedAt }
            });

            // Delete all sessions and tokens for this user
            await tx.session.deleteMany({
                where: { userId }
            });

            await tx.passwordResetToken.deleteMany({
                where: { userId }
            });

            await tx.emailVerificationToken.deleteMany({
                where: { userId }
            });
        });

        // Clear the session cookie
        const sessionCookie = await lucia.createBlankSessionCookie();
        cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

        return { success: true };
    } catch (error) {
        console.error('Error deleting account:', error);
        return { success: false, error: "Common.message.somethingWentWrong" };
    }
};