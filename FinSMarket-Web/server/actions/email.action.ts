'use server'

import * as Sentry from "@sentry/nextjs";
import { getUser } from "@/lib/lucia"
import { prisma } from "@/lib/prisma"
import { generateVerificationToken, sendEmail } from "@/lib/email"
import { ContactUsFormValues } from "@/types/validations/contact-us"
import { getLocale, getTranslations } from "next-intl/server"
import { i18Keys } from "@/global"

export const sendVerificationEmail = async () => {
    let locale = "en";

    try {
        locale = await getLocale();
    } catch (error) {
        console.log("error: ", error);
    }

    // TODO: fix not able to get locale from server and enhance now manually set to en
    const t = await getTranslations({ locale: locale || "en" });

    const MINUTES_TO_EXPIRE = 15;
    const MILLISECONDS_PER_MINUTE = 60 * 1000;
    const EXPIRE_TIME = MINUTES_TO_EXPIRE * MILLISECONDS_PER_MINUTE;

    try {
        const redirectOnUnverifiedUser = false;

        const user = await getUser(redirectOnUnverifiedUser);
        if (!user) {
            return { error: "User not found", success: false }
        }

        const newToken = await generateVerificationToken();
        const verificationLink = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/verify-email?token=${newToken}`;

        await prisma.emailVerificationToken.create({
            data: {
                token: newToken,
                userId: user.id,
                expiresAt: (() => {
                    return new Date(Date.now() + EXPIRE_TIME);
                })()
            }
        });

        const emailFrom = `"FinSMarket" <<EMAIL>>`;
        const emailSubject = `[FinSMarket] ${t("Email.VerifyEmail.subject")}`;
        const emailText = `${t("Email.VerifyEmail.Message.pleaseClick")}: ${verificationLink}. ${t("Email.VerifyEmail.Message.pasteLink")}: ${verificationLink}. ${t("Email.VerifyEmail.Message.ignore")}`;
        const emailHtml = `
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${t("Email.VerifyEmail.subject")}</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 1rem; background-color: #f6f6f6;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; text-align: center; background-color: #fff; border-radius: 6px; border: 1px solid #eee; -webkit-box-shadow: 0px 3px 6px 2px rgba(0,0,0,0.2); -moz-box-shadow: 0px 3px 6px 2px rgba(0,0,0,0.2); box-shadow: 0px 3px 6px 2px rgba(0,0,0,0.2);">
                    <img src="https://lh3.googleusercontent.com/d/1IPyJPMoIzuNNHz386IF7Q4q5BnuI4any" style="width: 300px; height: auto; margin-bottom: 20px;" alt="FinSMarket Logo"/>
                    <h2 style="color: #000;">${t("Email.VerifyEmail.Message.header")}</h2>
                    <p>${t.markup("Email.VerifyEmail.Message.welcome", { link: (chunks) => `<a style="color: #FF9339;" href="${process.env.NEXT_PUBLIC_SITE_URL}">${chunks}</a>` })}, ${t("Email.VerifyEmail.Message.pleaseClick")}</p>
                    <p>
                        <a href="${verificationLink}" style="display: inline-block; padding: 10px 20px; background-color: #FF9339; color: #ffffff; text-decoration: none; border-radius: 3px;">${t("Email.VerifyEmail.Message.cta")}</a>
                    </p>
                    <div style="height: 1px; background-color: #eee; margin: 20px 0;"></div>
                    <p style="font-size: 12px; color: #888; margin-top: 20px;">${t("Email.VerifyEmail.Message.pasteLink")}</p>
                    <a href="${verificationLink}" style="font-size: 12px; color: #888; margin-top: 6px;">${verificationLink}</a>
                    <div style="height: 1px; background-color: #eee; margin: 20px 0;"></div>
                    <p style="font-size: 12px; color: #888; margin-top: 20px;">${t("Email.VerifyEmail.Message.ignore")}</p>
                </div>
            </body>
            </html>
        `;

        await sendEmail(
            emailFrom,
            user.email,
            emailSubject,
            emailText,
            emailHtml
        );

        return { success: true }
    } catch (error) {
        console.log("error: ", error);
        Sentry.captureException(error);
        return { error: 'Common.message.somethingWentWrong', success: false }
    }
}

export const sendContactUsEmail = async (data: ContactUsFormValues) => {
    const t = await getTranslations();
    const SERVICE_EMAIL = "<EMAIL>";

    try {
        const topicString = data.topics.map(topic => `${t(`Email.ContactUs.Topics.${topic}` as i18Keys)} (${topic})`).join(", ");

        const emailFrom = `"FinSMarket" <<EMAIL>>`;
        const emailSubject = `[FinSMarket] ${t("Email.ContactUs.subject")}`;
        const emailText = `${t("Email.ContactUs.Message.hi")} ${data.name}, ${t("Email.ContactUs.Message.sentence_1")} ${t("Email.ContactUs.Message.sentence_2")} ${t("Email.ContactUs.Message.received")}: ${t("Email.ContactUs.Message.topic")}: ${topicString} ${t("Email.ContactUs.Message.detail")}: ${data.message}`;
        const emailHtml = `
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${t("Email.ContactUs.subject")}</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 1rem; background-color: #f6f6f6;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; text-align: center; background-color: #fff; border-radius: 6px; border: 1px solid #eee; -webkit-box-shadow: 0px 3px 6px 2px rgba(0,0,0,0.2); -moz-box-shadow: 0px 3px 6px 2px rgba(0,0,0,0.2); box-shadow: 0px 3px 6px 2px rgba(0,0,0,0.2);">
                    <img src="https://lh3.googleusercontent.com/d/1IPyJPMoIzuNNHz386IF7Q4q5BnuI4any" style="width: 300px; height: auto; margin-bottom: 20px;" alt="FinSMarket Logo"/>
                    <div style="text-align: left; color: #333;">
                        <p>${t("Email.ContactUs.Message.hi")} ${data.name},</p>
                        <p>
                            ${t("Email.ContactUs.Message.sentence_1")}
                        </p>

                        <div style="height: 1px; background-color: #eee; margin: 20px 0;"></div>

                        <p>${t("Email.ContactUs.Message.received")}:</p>
                        <div style="font-size: 12px;">
                            <p>
                                <span style="font-weight: bold;">${t("Email.ContactUs.Message.topic")}:</span> <span style="color: #8A8A8A">${topicString}</span>
                            </p>
                            <p>
                                <span style="font-weight: bold;">${t("Email.ContactUs.Message.detail")}:</span>
                            </p>
                            <p>
                                <span style="color: #8A8A8A; white-space: pre-line">
                                    ${data.message}
                                </span>
                            </p>
                        </div>

                        <div style="height: 1px; background-color: #eee; margin: 20px 0;"></div>

                        <p>
                            ${t("Email.ContactUs.Message.sentence_2")}
                        </p>

                        <p>${t("Email.Common.bestRegards")},</p>
                        <p>FinSMarket ${t("Email.Common.supportTeam")}</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        await sendEmail(
            emailFrom,
            data.email,
            emailSubject,
            emailText,
            emailHtml,
            {
                cc: SERVICE_EMAIL,
                replyTo: SERVICE_EMAIL,
            },
        );

        return { success: true }
    } catch (error) {
        console.log("error: ", error);
        Sentry.captureException(error);
        return { error: 'Common.message.somethingWentWrong', success: false }
    }
}
