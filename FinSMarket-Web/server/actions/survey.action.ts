'use server'

import { SubmissionPayload } from "@/components/profile/survey/tally-form"
import { apiPath } from "@/config/api";
import { clientApi } from "@/lib/api-client";
import { getUser } from "@/lib/lucia";
import SurveyRepo from "@/repository/survey";
import UserPermissionRepo from "@/repository/user-permission";
import { Permission } from "@prisma/client";
import { headers } from "next/headers";
import { endOfDay, lastDayOfYear } from "date-fns";

export type HandleSubmitSurveyResponse = {
    success: boolean,
    data?: any,
    message?: string,
}

export const handleSubmitSurvey = async (payload: SubmissionPayload, sessionTokenId?: string, isMobileApp?: boolean): Promise<HandleSubmitSurveyResponse> => {
    try {
        const user = await getUser(false, sessionTokenId, isMobileApp); // Don't redirect on unverified user for mobile app access
        if (!user) {
            throw new Error("Cannot get user");
        }

        if (await SurveyRepo.isUserFilled(user.id, payload.formId)) {
            throw new Error("User has filled this survey already");
        }
        
        await clientApi.post(apiPath.client.ZENTA.report.active, {
            email: user.email,
        }, {
            // withCredentials: true,
            // headers: {
            //     Cookie: `_session=${sessionCookie}`
            // }
        });

        const response = await SurveyRepo.storeSubmission(user.id, payload.formId, payload);
        await UserPermissionRepo.grant(user.id, Permission.free_report_2024, new Date());
        // await UserPermissionRepo.grant(user.id, Permission.free_report_2024, new Date(), endOfDay(lastDayOfYear(new Date("2024"))));

        return response;
    }
    catch (error) {
        console.error("Survey submission error:", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "Something went wrong",
        };
    }
}