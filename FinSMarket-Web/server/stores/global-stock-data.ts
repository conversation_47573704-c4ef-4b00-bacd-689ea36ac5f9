import { apiPath } from "@/config/api";
import { isAfterMarketClose, isMarketOpen } from "@/utils/stock-market";
import { aiDatabaseApi } from "@/lib/api/ai-database-api-client";
import { StocksPriceResponse } from "@/types/api/ai-database";
import { MissionType } from "@/types/api/zenta";

export type CustomStockData = {
    symbol: string;
    lastPrice: number;
    timestamp: Date;
    // volume: number;
    // askPrice: number;
    // bidPrice: number;
};

const PRICE_STALENESS_THRESHOLD = 5000; // 5 seconds in milliseconds


// ====================== GlobalStockData Class ======================
// Manages global stock data, including data caching, updates, and retrieval
class GlobalStockData {
    // Singleton instance
    private static instance: GlobalStockData;
    private latestData: Map<string, CustomStockData> = new Map(); // Cache latest stock data, key is stock symbol (uppercase)
    private pendingRequests: Map<string, Promise<CustomStockData[]>> = new Map(); // Store pending API requests, key is sorted stock symbols string
    private lastRequestTime: number = 0; // Timestamp of last API request (milliseconds)
    private readonly MIN_REQUEST_INTERVAL = 1000; // Minimum request interval (1 second)
    
    
    // ====================== Singleton Configuration ======================
    // Private constructor, ensures instance is only obtained through getInstance method
    private constructor() {}
    
    
    /**
     * Get the singleton instance of GlobalStockData
     * @returns {GlobalStockData} GlobalStockData instance
     */
    public static getInstance(): GlobalStockData {
        if (!GlobalStockData.instance) {
            GlobalStockData.instance = new GlobalStockData();
        }
        return GlobalStockData.instance;
    }
    
    
    // ====================== Helper Methods ======================
    
    
    /**
     * Delay for specified milliseconds
     * @param {number} ms Delay in milliseconds
     * @returns {Promise<void>} Promise that resolves after delay
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    
    /**
     * Check if cached stock data is still fresh
     * @param {CustomStockData} cachedData Cached stock data
     * @param {number} currentTime Current timestamp in milliseconds
     * @param {boolean} isMarketOpenNow Whether market is currently open
     * @returns {boolean} Returns true if data is fresh, false otherwise
     */
    private isStockDataFresh(cachedData: CustomStockData, currentTime: number, isMarketOpenNow: boolean): boolean {
        const cachedTimestamp = cachedData.timestamp instanceof Date ? cachedData.timestamp.getTime() : null;
        if (!cachedTimestamp) return false;
        const timeDifference = currentTime - cachedTimestamp;
        
        // If market is open, check if data is within validity period; if closed, check if data was updated after market close
        return isMarketOpenNow ? timeDifference < PRICE_STALENESS_THRESHOLD : isAfterMarketClose(cachedData.timestamp);
    }
    
    
    // ====================== Data Update Methods ======================
    
    
    /**
     * Update stock data in cache
     * @param {CustomStockData} data Stock data to update
     *   - symbol: Stock symbol (will be converted to uppercase)
     *   - lastPrice: Latest price
     *   - timestamp: Data timestamp, will be converted to Date if not already
     */
    public updateStock(data: CustomStockData): void {
        // Convert stock symbol to uppercase
        const symbol = data.symbol.toUpperCase();
        
        // Get existing data from cache
        const existingData = this.latestData.get(symbol);
        
        // Only update cache if new data timestamp is later
        if (!existingData || data.timestamp > existingData.timestamp) {
            this.latestData.set(symbol, {
                ...data,
                // Ensure timestamp is a Date object
                timestamp: data.timestamp instanceof Date ? data.timestamp : new Date(data.timestamp),
            });
        }
    }
    
    
    /**
     * Fetch latest data for specified stock symbols from API
     * @param {string[]} symbols Array of stock symbols
     * @returns {Promise<CustomStockData[]>} Promise containing stock data
     */
    private async fetchStockData(symbols: string[]): Promise<CustomStockData[]> {
        try {
            // Send POST request to get stock price data
            const response: StocksPriceResponse = await aiDatabaseApi.post(`${apiPath.server.AI_DATABASE.stocksPrice}`, {
                mission_type: MissionType.stocksPrice,
                symbol_list: symbols,
            });
    
            if (!response || !response.response) {
                throw new Error(`Unable to get stock quotes for symbols: ${symbols.join(', ')}`);
            }
    
            const data = response.response;
            const result: CustomStockData[] = [];
    
            // Process each stock symbol
            for (const symbol of symbols) {
                const quote = data[symbol];
                const newData: CustomStockData = {
                    symbol: symbol,
                    lastPrice: quote || -1,  // Set to -1 if no quote available
                    timestamp: new Date(),    // Use current time as data timestamp
                };
    
                // Update stock data in cache
                this.updateStock(newData);
                result.push(newData);
            }
    
            return result;
        } catch (error) {
            console.error(`Error fetching stock data:`, (error as Error)?.message || "Unknown error");
            throw error;
        }
    }
    
    
    // ====================== Data Retrieval Methods ======================
    
    
    /**
     * Get latest data for multiple stocks
     * @param {string[]} symbols Array of stock symbols
     * @param {boolean} [skipCache=false] Whether to skip cache and fetch directly from API
     * @returns {Promise<CustomStockData[]>} Promise containing stock data
     */
    public async getStocks(symbols: string[], skipCache: boolean = false): Promise<CustomStockData[]> {
        // Use Map to store results, key is stock symbol
        const result = new Map<string, CustomStockData>();
        // Stock symbols to fetch from API
        const symbolsToFetch: string[] = [];
    
        // Convert all stock symbols to uppercase for uniform format
        symbols = symbols.map(s => s.toUpperCase());
    
        const currentTime = Date.now();
        const isMarketOpenNow = isMarketOpen();
    
        // Check each stock symbol for fresh cached data
        for (const symbol of symbols) {
            const cachedData = this.latestData.get(symbol);
    
            if (!skipCache && cachedData && this.isStockDataFresh(cachedData, currentTime, isMarketOpenNow)) {
                result.set(symbol, cachedData);
                continue;
            }
    
            // If cached data does not exist or is expired, need to fetch from API
            symbolsToFetch.push(symbol);
        }
    
        // If there are stocks to update, perform API request
        if (symbolsToFetch.length > 0) {
            // Sort stock symbols and generate unique request key
            const requestKey = symbolsToFetch.sort().join(',');
    
            let pendingRequest = this.pendingRequests.get(requestKey);
    
            // If corresponding request does not exist, create new request
            if (!pendingRequest) {
                const timeSinceLastRequest = currentTime - this.lastRequestTime;
                if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
                    // Delay to meet minimum request interval requirement
                    await this.delay(this.MIN_REQUEST_INTERVAL - timeSinceLastRequest);
                }
    
                pendingRequest = this.fetchStockData(symbolsToFetch);
                this.pendingRequests.set(requestKey, pendingRequest);
    
                // After request completes, remove record from pendingRequests and update lastRequestTime
                pendingRequest.finally(() => {
                    this.pendingRequests.delete(requestKey);
                    this.lastRequestTime = Date.now();
                });
            }
    
            try {
                const fetchedData = await pendingRequest;
                // Merge fetched data into results
                fetchedData.forEach(data => result.set(data.symbol, data));
            } catch (error) {
                console.error(`Failed to fetch stock data for symbols: ${symbolsToFetch.join(', ')}`);
            }
        }
    
        return Array.from(result.values());
    }
    
    
    /**
     * Get latest data for a single stock
     * @param {string} symbol Stock symbol
     * @param {boolean} [skipCache=false] Whether to skip cache and fetch directly from API
     * @returns {Promise<CustomStockData | null>} Promise containing stock data or null if not found
     */
    public async getStock(symbol: string, skipCache: boolean = false): Promise<CustomStockData | null> {
        const result = await this.getStocks([symbol], skipCache);
        return result[0] || null;
    }
    
    
    /**
     * Get all stock data from cache
     * @returns {Map<string, CustomStockData>} Copy of all cached stock data Map
     */
    public getAllStocks(): Map<string, CustomStockData> {
        // Return a copy of cached data to prevent external state modification
        return new Map(this.latestData);
    }
}

// Export GlobalStockData singleton instance
export const globalStockData = GlobalStockData.getInstance();
