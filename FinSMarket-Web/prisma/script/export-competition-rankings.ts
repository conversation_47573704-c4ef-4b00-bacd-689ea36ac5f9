import { PrismaClient } from "@prisma/client";
import * as XLSX from "xlsx";
import { exit } from "process";
import path from "path";
import { fromZonedTime } from "date-fns-tz";
import { COMPETITION_TIMEZONE } from "@/utils/stock-market";
import fs from 'fs';

const prisma = new PrismaClient();

export type RankingResult = {
  rank: number;
  userId: string;
  userEmail: string;
  userName: string;
  portfolioId: number;
  initialBalance: number;
  finalBalance: number;
  profit: number;
  profitRate: number;
};

async function calculateRankings(startDate: Date, endDate: Date): Promise<RankingResult[]> {
  const portfolios = await prisma.portfolio.findMany({
    where: {
      competitionEntry: {
        competition: {
          startDate: {
            lte: endDate,
          },
          endDate: {
            gte: startDate,
          },
        },
      },
    },
    include: {
      user: {
        select: {
          email: true,
          firstName: true,
          lastName: true,
        },
      },
      snapshots: {
        where: {
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          date: 'asc',
        },
      },
    },
  });

  const results = portfolios
    .filter(portfolio => portfolio.snapshots.length >= 2)
    .map(portfolio => {
      const initialSnapshot = portfolio.snapshots[0];
      const finalSnapshot = portfolio.snapshots[portfolio.snapshots.length - 1];
      
      const competitionStart = fromZonedTime(new Date('2024-11-01 00:00:00'), COMPETITION_TIMEZONE);
      const isCompetitionStart = startDate.getTime() === competitionStart.getTime();
      
      const initialValue = (isCompetitionStart)
        ? portfolio.initialBalance 
        : initialSnapshot.totalValue;
      
      const profit = finalSnapshot.totalValue - initialValue;
      const profitRate = (profit / initialValue) * 100;

      return {
        userId: portfolio.userId,
        userEmail: portfolio.user.email,
        userName: `${portfolio.user.firstName} ${portfolio.user.lastName}`,
        portfolioId: portfolio.id,
        initialBalance: initialValue,
        finalBalance: finalSnapshot?.totalValue || 0,
        profit,
        profitRate,
        rank: 0, // Will be calculated later
      };
    });

  // Sort by profit and assign ranks
  return results
    .sort((a, b) => b.profit - a.profit)
    .map((result, index) => ({
      ...result,
      rank: index + 1,
    }));
}

async function exportToTypeScript(
  overallRankings: RankingResult[],
  novemberRankings: RankingResult[],
  decemberRankings: RankingResult[],
) {
  const content = `// Auto-generated file. Do not edit manually.
import { RankingResult } from "@/prisma/script/export-competition-rankings";

export const novemberRankings: RankingResult[] = ${JSON.stringify(novemberRankings, null, 2)};

export const decemberRankings: RankingResult[] = ${JSON.stringify(decemberRankings, null, 2)};

export const overallRankings: RankingResult[] = ${JSON.stringify(overallRankings, null, 2)};
`;

  const filePath = path.join(process.cwd(), 'exports', 'final-rankings.ts');
  fs.writeFileSync(filePath, content, 'utf-8');
  return filePath;
}

async function exportToExcel(
  overallRankings: RankingResult[],
  novemberRankings: RankingResult[],
  decemberRankings: RankingResult[],
) {
  const workbook = XLSX.utils.book_new();

  // Convert rankings to worksheet format
  const formatRankings = (rankings: RankingResult[]) => 
    rankings.map(r => ({
      Rank: r.rank,
      Email: r.userEmail,
      Name: r.userName,
      'Portfolio ID': r.portfolioId,
      'Initial Balance': r.initialBalance.toFixed(2),
      'Final Balance': r.finalBalance.toFixed(2),
      'Profit': r.profit.toFixed(2),
      'Profit Rate (%)': r.profitRate.toFixed(2),
    }));

  // Define column widths
  const columnWidths = [
    { wch: 6 },    // Rank
    { wch: 35 },   // Email
    { wch: 25 },   // Name
    { wch: 12 },   // Portfolio ID
    { wch: 15 },   // Initial Balance
    { wch: 15 },   // Final Balance
    { wch: 15 },   // Profit
    { wch: 15 },   // Profit Rate
  ];

  // Create and format worksheets
  const createFormattedSheet = (data: any[]) => {
    const worksheet = XLSX.utils.json_to_sheet(data);
    worksheet['!cols'] = columnWidths;
    return worksheet;
  };

  // Create worksheets with formatting
  const overallWS = createFormattedSheet(formatRankings(overallRankings));
  const novemberWS = createFormattedSheet(formatRankings(novemberRankings));
  const decemberWS = createFormattedSheet(formatRankings(decemberRankings));

  // Add worksheets to workbook
  XLSX.utils.book_append_sheet(workbook, overallWS, "Overall Rankings");
  XLSX.utils.book_append_sheet(workbook, novemberWS, "November Rankings");
  XLSX.utils.book_append_sheet(workbook, decemberWS, "December Rankings");

  // Save workbook
  const filename = `competition_website_rankings_${new Date().toISOString().split('T')[0]}.xlsx`;
  const filePath = path.join(process.cwd(), 'exports', filename);
  XLSX.writeFile(workbook, filePath);
  
  return filePath;
}

async function main() {
  try {
    const competitionStart = fromZonedTime(new Date('2024-11-01 00:00:00'), COMPETITION_TIMEZONE);
    const novemberEnd = fromZonedTime(new Date('2024-11-30 23:59:59'), COMPETITION_TIMEZONE);
    const decemberStart = fromZonedTime(new Date('2024-11-29 00:00:00'), COMPETITION_TIMEZONE);
    const competitionEnd = fromZonedTime(new Date('2024-12-31 23:59:59'), COMPETITION_TIMEZONE);

    console.log('Calculating rankings...');
    const overallRankings = await calculateRankings(competitionStart, competitionEnd);
    const novemberRankings = await calculateRankings(competitionStart, novemberEnd);
    const decemberRankings = await calculateRankings(decemberStart, competitionEnd);

    // Create exports directory if it doesn't exist
    const exportsDir = path.join(process.cwd(), 'exports');
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    // Export to both formats
    const excelFile = await exportToExcel(
      overallRankings,
      novemberRankings,
      decemberRankings
    );
    const tsFile = await exportToTypeScript(
      overallRankings,
      novemberRankings,
      decemberRankings
    );

    console.log(`Rankings exported successfully to Excel: ${excelFile}`);
    console.log(`Rankings exported successfully to TypeScript: ${tsFile}`);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch((e) => {
  console.error(e);
  prisma.$disconnect();
  exit(1);
}); 