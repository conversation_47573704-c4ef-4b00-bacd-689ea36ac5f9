import { PrismaClient, Transaction } from "@prisma/client";
import * as XLSX from "xlsx";
import { exit } from "process";
import path from "path";
import { fromZonedTime, toZonedTime } from "date-fns-tz";
import { COMPETITION_TIMEZONE } from "@/utils/stock-market";
import fs from 'fs';
import { format } from 'date-fns';

const prisma = new PrismaClient();

type SuspiciousUser = {
  userId: string;
  email: string;
  name: string;
  portfolioId: number;
  returnsRate: number;
  transactions: Transaction[];
}

async function getSuspiciousUsers(): Promise<SuspiciousUser[]> {
  // Get users with >45% returns from the competition
  const highReturnPortfolios = await prisma.portfolio.findMany({
    where: {
      competitionEntry: {
        isNot: null,
      },
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
        },
      },
      snapshots: {
        orderBy: {
          date: 'asc',
        },
      },
    },
  });

  const suspiciousUsers = highReturnPortfolios
    .filter(portfolio => {
      if (portfolio.snapshots.length < 2) return false;
      const initialSnapshot = portfolio.snapshots[0];
      const finalSnapshot = portfolio.snapshots[portfolio.snapshots.length - 1];
      const returnsRate = ((finalSnapshot.totalValue - initialSnapshot.totalValue) / initialSnapshot.totalValue) * 100;
      return returnsRate > 45;
    })
    .map(portfolio => ({
      userId: portfolio.user.id,
      email: portfolio.user.email,
      name: `${portfolio.user.firstName} ${portfolio.user.lastName}`,
      portfolioId: portfolio.id,
      returnsRate: ((portfolio.snapshots[portfolio.snapshots.length - 1].totalValue - portfolio.snapshots[0].totalValue) / portfolio.snapshots[0].totalValue) * 100,
      transactions: [], // Will be filled later
    }));

  // Get transactions for each suspicious user
  for (const user of suspiciousUsers) {
    const transactions = await prisma.transaction.findMany({
      where: {
        portfolioId: user.portfolioId,
      },
      orderBy: {
        timestamp: 'asc',
      },
    });

    // Filter transactions to only include those within first 1.5 hours of market open each day
    user.transactions = transactions.filter(transaction => {
      const txDate = toZonedTime(transaction.timestamp, COMPETITION_TIMEZONE);
      const hours = txDate.getHours();
      const minutes = txDate.getMinutes();
      
      // Convert to minutes since market open (9:30 AM)
      const minutesSinceOpen = (hours - 9) * 60 + (minutes - 30);
      
      // Keep only transactions between 9:30 AM and 11:00 AM (first 90 minutes)
      return minutesSinceOpen >= 0 && minutesSinceOpen <= 90;
    }) as typeof user.transactions;
  }

  return suspiciousUsers.sort((a, b) => b.returnsRate - a.returnsRate);
}

async function exportToExcel(suspiciousUsers: SuspiciousUser[]) {
  const workbook = XLSX.utils.book_new();

  for (const user of suspiciousUsers) {
    // Format transactions for display
    const formattedTransactions = user.transactions.map(t => ({
      Date: format(toZonedTime(t.timestamp, COMPETITION_TIMEZONE), 'yyyy-MM-dd'),
      Time: format(toZonedTime(t.timestamp, COMPETITION_TIMEZONE), 'HH:mm:ss'),
      Symbol: t.symbol,
      Type: t.type,
      Shares: t.shares,
      Price: t.price.toFixed(2),
      'Total Value': (t.shares * t.price).toFixed(2),
    }));

    // Skip users with no suspicious transactions
    if (formattedTransactions.length === 0) continue;

    // Create worksheet with user info header
    // const userInfo = {
    //   Date: `User: ${user.name}`,
    //   Time: `Email: ${user.email}`,
    //   Symbol: `Portfolio ID: ${user.portfolioId}`,
    //   Type: `Returns: ${user.returnsRate.toFixed(1)}%`,
    //   Shares: '',
    //   Price: '',
    //   'Total Value': '',
    // };

    const userInfo = [
        { Date: 'User Email:', Time: user.email },
        { Date: 'User Name:', Time: user.name },
        { Date: 'Portfolio ID:', Time: user.portfolioId.toString() },
        { Date: 'Returns Rate:', Time: `${user.returnsRate.toFixed(1)}%` },
        { Date: '', Time: '' }, // Empty row for spacing
    ];

    // Combine user info and transactions
    const worksheetData = [...userInfo, ...formattedTransactions];
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);

    // Set column widths
    worksheet['!cols'] = [
      { wch: 20 }, // Date
      { wch: 20 }, // Time
      { wch: 15 }, // Symbol
      { wch: 10 }, // Type
      { wch: 10 }, // Shares
      { wch: 12 }, // Price
      { wch: 12 }, // Total Value
    ];

    // Create worksheet name: "(50.5%) John Doe"
    const worksheetName = `(${user.returnsRate.toFixed(1)}%) ${user.name}`.substring(0, 31);
    XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
  }

  // Save workbook
  const filename = `suspicious_transactions_${new Date().toISOString().split('T')[0]}.xlsx`;
  const filePath = path.join(process.cwd(), 'exports', filename);
  
  // Create exports directory if it doesn't exist
  const exportsDir = path.join(process.cwd(), 'exports');
  if (!fs.existsSync(exportsDir)) {
    fs.mkdirSync(exportsDir, { recursive: true });
  }

  XLSX.writeFile(workbook, filePath);
  return filePath;
}

async function main() {
  try {
    console.log('Finding suspicious users and their transactions...');
    const suspiciousUsers = await getSuspiciousUsers();

    console.log(`Found ${suspiciousUsers.length} users with >50% returns`);
    
    const excelFile = await exportToExcel(suspiciousUsers);
    console.log(`Results exported successfully to: ${excelFile}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch((e) => {
  console.error(e);
  prisma.$disconnect();
  exit(1);
});
