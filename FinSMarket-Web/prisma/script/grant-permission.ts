import { userHasPermissionTo } from "@/lib/user";
import { PrismaClient, Permission } from "@prisma/client";
import { exit } from "process";

const prisma = new PrismaClient();

async function grantPermission(permission: string, emails: string[]) {
    // Check if the permission is valid
    if (!Object.values(Permission).includes(permission as Permission)) {
        console.error(`Error: Invalid permission "${permission}"`);
        exit(1);
    }

    for (const email of emails) {
        try {
            const user = await prisma.user.findUnique({ where: { email }, include: { permissions: true } });
            if (!user) {
                console.log(
                    `Grant permission [${permission}] to [${email}]: failed (User not found)`
                );
                continue;
            }

            if (userHasPermissionTo(user.permissions, permission as Permission)) {
                console.log(`Grant permission [${permission}] to [${email}]: failed (User already has permission)`);
                continue;
            }

            await prisma.userPermission.create({
                data: {
                    userId: user.id,
                    permission: permission as Permission,
                    startAt: new Date(),
                },
            });

            console.log(`Grant permission [${permission}] to [${email}]: succeed`);
        } catch (error) {
            console.log(
                `Grant permission [${permission}] to [${email}]: failed (${error instanceof Error ? error.message : JSON.stringify(error)})`
            );
        }
    }
}

async function main() {
    const args = process.argv.slice(2);

    if (args.length < 2) {
        console.error(
            "Usage: ... <permission> <email1> [email2] [email3] ..."
            // "Usage: npx ts-node scripts/grant-permission.ts <permission> <email1> [email2] [email3] ..."
        );
        exit(1);
    }

    const [permission, ...emails] = args;
    await grantPermission(permission, emails);
    await prisma.$disconnect();
}

main().catch((e) => {
    console.error(e);
    prisma.$disconnect();
    exit(1);
});
