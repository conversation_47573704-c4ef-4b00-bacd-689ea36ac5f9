import { PrismaClient, Transaction } from "@prisma/client";
import * as XLSX from "xlsx";
import { exit } from "process";
import path from "path";
import { fromZonedTime, toZonedTime } from "date-fns-tz";
import { COMPETITION_TIMEZONE } from "@/utils/stock-market";
import fs from 'fs';
import { format, differenceInMinutes } from 'date-fns';

const prisma = new PrismaClient();

type InvalidTransaction = {
  buy: Transaction;
  sell: Transaction;
  invalidProfit: number;
  profitPercentage: number;
}

type SuspiciousUser = {
  userId: string;
  email: string;
  name: string;
  portfolioId: number;
  originalReturnsRate: number;
  originalProfit: number;
  initialValue: number;
  adjustedReturnsRate: number;
  totalInvalidProfit: number;
  transactions: Transaction[];
  invalidTransactions: InvalidTransaction[];
}

type ExcelRow = {
  Date: string;
  Time: string;
  Symbol?: string;
  Type?: string;
  Shares?: string | number;
  Price?: string | number;
  'Total Value'?: string | number;
  'Invalid'?: string;
}

function findInvalidTransactions(transactions: Transaction[]): InvalidTransaction[] {
  const invalidTxs: InvalidTransaction[] = [];
  const txsBySymbol: { [symbol: string]: Transaction[] } = {};

  // Group transactions by symbol and date
  transactions.forEach(tx => {
    const txDate = toZonedTime(tx.timestamp, COMPETITION_TIMEZONE);
    const dateKey = format(txDate, 'yyyy-MM-dd');
    const key = `${tx.symbol}_${dateKey}`;
    
    if (!txsBySymbol[key]) {
      txsBySymbol[key] = [];
    }
    txsBySymbol[key].push(tx);
  });

  // For each symbol and date, find invalid buy-sell pairs
  Object.values(txsBySymbol).forEach(symbolTxs => {
    const sortedTxs = symbolTxs.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    // Track price movement patterns
    let lastPrice = 0;
    let priceUpdateCount = 0;
    let isRealTimeWorking = false;
    
    // First pass: detect if real-time prices are working by checking price update patterns
    for (let i = 0; i < sortedTxs.length - 1; i++) {
      const currentTx = sortedTxs[i];
      const nextTx = sortedTxs[i + 1];
      
      if (lastPrice > 0) {
        const priceChange = Math.abs((currentTx.price - lastPrice) / lastPrice);
        // If we see small, incremental price changes, real-time is likely working
        if (priceChange > 0.001 && priceChange < 0.05) {
          priceUpdateCount++;
        }
      }
      lastPrice = currentTx.price;
      
      // If we see consistent small price updates, mark real-time as working
      if (priceUpdateCount >= 3) {
        isRealTimeWorking = true;
        break;
      }
    }
    
    // If real-time prices are working, skip this symbol
    if (isRealTimeWorking) {
      return;
    }
    
    // Reset for actual invalid transaction detection
    lastPrice = 0;
    
    for (let i = 0; i < sortedTxs.length - 1; i++) {
      const buyTx = sortedTxs[i];
      if (buyTx.type !== 'BUY') continue;

      // Check if buy transaction is within first 30 minutes of market open
      const buyTime = toZonedTime(buyTx.timestamp, COMPETITION_TIMEZONE);
      const buyHour = buyTime.getHours();
      const buyMinutes = buyTime.getMinutes();
      const minutesSinceOpen = (buyHour - 9) * 60 + (buyMinutes - 30);
      
      // Only check transactions in the first 30 minutes of trading
      if (minutesSinceOpen < 0 || minutesSinceOpen > 30) continue;

      // Look for matching sell transactions
      for (let j = i + 1; j < sortedTxs.length; j++) {
        const sellTx = sortedTxs[j];
        if (sellTx.type !== 'SELL') continue;

        const timeDiff = differenceInMinutes(
          toZonedTime(sellTx.timestamp, COMPETITION_TIMEZONE),
          toZonedTime(buyTx.timestamp, COMPETITION_TIMEZONE)
        );

        // Only look at sells within 60 minutes of buy
        if (timeDiff > 60) continue;

        // Calculate profit percentage
        const profitPercentage = ((sellTx.price - buyTx.price) / buyTx.price) * 100;
        
        // Only consider as suspicious if:
        // 1. Profit > 20%
        // 2. No gradual price movement pattern detected
        if (profitPercentage > 20) {
          // Check if there were intermediate transactions showing gradual price movement
          const intermediateTxs = sortedTxs.filter(tx => 
            tx.timestamp > buyTx.timestamp && 
            tx.timestamp < sellTx.timestamp
          );
          
          // If we see gradual price movement, skip this pair
          if (intermediateTxs.length >= 2) {
            const hasGradualMovement = intermediateTxs.every((tx, idx, arr) => {
              if (idx === 0) return true;
              const prevTx = arr[idx - 1];
              const priceChange = Math.abs((tx.price - prevTx.price) / prevTx.price);
              return priceChange < 0.05; // Less than 5% change between consecutive transactions
            });
            
            if (hasGradualMovement) continue;
          }

          const suspiciousShares = Math.min(buyTx.shares, sellTx.shares);
          const excessiveProfit = sellTx.price - (buyTx.price * 1.20); // Amount above 20% profit
          const invalidProfit = suspiciousShares * excessiveProfit;
          
          invalidTxs.push({
            buy: buyTx,
            sell: sellTx,
            invalidProfit,
            profitPercentage
          });
        }
      }
    }
  });

  return invalidTxs;
}

async function getSuspiciousUsers(): Promise<SuspiciousUser[]> {
  // Get users with >45% returns from the competition
  const highReturnPortfolios = await prisma.portfolio.findMany({
    where: {
      competitionEntry: {
        isNot: null,
      },
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
        },
      },
      snapshots: {
        orderBy: {
          date: 'asc',
        },
      },
    },
  });

  const suspiciousUsers = highReturnPortfolios
    .filter(portfolio => {
      if (portfolio.snapshots.length < 2) return false;
      const initialSnapshot = portfolio.snapshots[0];
      const finalSnapshot = portfolio.snapshots[portfolio.snapshots.length - 1];
      const returnsRate = ((finalSnapshot.totalValue - initialSnapshot.totalValue) / initialSnapshot.totalValue) * 100;
      return returnsRate > 45;
    })
    .map(portfolio => {
      const initialValue = portfolio.snapshots[0].totalValue;
      const finalValue = portfolio.snapshots[portfolio.snapshots.length - 1].totalValue;
      const originalReturnsRate = ((finalValue - initialValue) / initialValue) * 100;
      const originalProfit = finalValue - initialValue;
      
      return {
        userId: portfolio.user.id,
        email: portfolio.user.email,
        name: `${portfolio.user.firstName} ${portfolio.user.lastName}`,
        portfolioId: portfolio.id,
        originalReturnsRate,
        originalProfit,
        initialValue,
        adjustedReturnsRate: originalReturnsRate, // Will be updated later
        totalInvalidProfit: 0, // Will be updated later
        transactions: [] as Transaction[],
        invalidTransactions: [] as InvalidTransaction[],
      } satisfies SuspiciousUser;
    });

  // Get transactions for each suspicious user
  for (const user of suspiciousUsers) {
    const transactions = await prisma.transaction.findMany({
      where: {
        portfolioId: user.portfolioId,
      },
      orderBy: {
        timestamp: 'asc',
      },
    });

    // Filter transactions to only include those within first 1.5 hours of market open each day
    user.transactions = transactions.filter(transaction => {
      const txDate = toZonedTime(transaction.timestamp, COMPETITION_TIMEZONE);
      const hours = txDate.getHours();
      const minutes = txDate.getMinutes();
      
      // Convert to minutes since market open (9:30 AM)
      const minutesSinceOpen = (hours - 9) * 60 + (minutes - 30);
      
      // Keep only transactions between 9:30 AM and 11:00 AM (first 90 minutes)
      return minutesSinceOpen >= 0 && minutesSinceOpen <= 90;
    });

    // Find invalid transactions
    user.invalidTransactions = findInvalidTransactions(user.transactions);
    user.totalInvalidProfit = user.invalidTransactions.reduce((sum, tx) => sum + tx.invalidProfit, 0);

    // Calculate adjusted returns rate
    user.adjustedReturnsRate = ((user.originalProfit - user.totalInvalidProfit) / user.initialValue) * 100;
  }

  return suspiciousUsers.sort((a, b) => b.originalReturnsRate - a.originalReturnsRate);
}

async function exportToExcel(suspiciousUsers: SuspiciousUser[]) {
  const workbook = XLSX.utils.book_new();

  for (const user of suspiciousUsers) {
    // Format transactions for display
    const formattedTransactions = user.transactions.map(t => {
      const isInvalid = user.invalidTransactions.some(
        invalid => invalid.buy.id === t.id || invalid.sell.id === t.id
      );

      const row: ExcelRow = {
        Date: format(toZonedTime(t.timestamp, COMPETITION_TIMEZONE), 'yyyy-MM-dd'),
        Time: format(toZonedTime(t.timestamp, COMPETITION_TIMEZONE), 'HH:mm:ss'),
        Symbol: t.symbol,
        Type: t.type,
        Shares: t.shares,
        Price: t.price.toFixed(2),
        'Total Value': (t.shares * t.price).toFixed(2),
        'Invalid': isInvalid ? '⚠️' : '',
      };
      return row;
    });

    // Skip users with no suspicious transactions
    if (formattedTransactions.length === 0) continue;

    const userInfo: ExcelRow[] = [
        { Date: 'User Email:', Time: user.email },
        { Date: 'User Name:', Time: user.name },
        { Date: 'Portfolio ID:', Time: user.portfolioId.toString() },
        { Date: 'Original Profit:', Time: `$${user.originalProfit.toFixed(2)}` },
        { Date: 'Original Returns:', Time: `${user.originalReturnsRate.toFixed(1)}%` },
        { Date: 'Invalid Profit:', Time: `$${user.totalInvalidProfit.toFixed(2)}` },
        { Date: 'Adjusted Profit:', Time: `$${(user.originalProfit - user.totalInvalidProfit).toFixed(2)}` },
        { Date: 'Adjusted Returns:', Time: `${user.adjustedReturnsRate.toFixed(1)}%` },
        { Date: '', Time: '' }, // Empty row for spacing
        { 
          Date: 'Invalid Transactions:', 
          Time: `${user.invalidTransactions.length} pairs found`,
          Symbol: 'Total Invalid:',
          Type: `$${user.totalInvalidProfit.toFixed(2)}`,
        },
        { Date: '', Time: '' }, // Empty row for spacing
    ];

    // Add invalid transaction details if any exist
    if (user.invalidTransactions.length > 0) {
      userInfo.push({
        Date: 'Buy Time',
        Time: 'Sell Time',
        Symbol: 'Symbol',
        Type: 'Shares',
        Shares: 'Buy Price',
        Price: 'Sell Price',
        'Total Value': 'Profit %',
        'Invalid': 'Invalid Profit',
      });

      user.invalidTransactions.forEach(invalid => {
        userInfo.push({
          Date: format(toZonedTime(invalid.buy.timestamp, COMPETITION_TIMEZONE), 'HH:mm:ss'),
          Time: format(toZonedTime(invalid.sell.timestamp, COMPETITION_TIMEZONE), 'HH:mm:ss'),
          Symbol: invalid.buy.symbol,
          Type: Math.min(invalid.buy.shares, invalid.sell.shares).toString(),
          Shares: invalid.buy.price.toFixed(2),
          Price: invalid.sell.price.toFixed(2),
          'Total Value': `${invalid.profitPercentage.toFixed(1)}%`,
          'Invalid': `$${invalid.invalidProfit.toFixed(2)}`,
        });
      });

      userInfo.push({ Date: '', Time: '' }); // Empty row for spacing
    }

    // Combine user info and transactions
    const worksheetData = [...userInfo, ...formattedTransactions];
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);

    // Set column widths
    worksheet['!cols'] = [
      { wch: 20 }, // Date
      { wch: 20 }, // Time
      { wch: 15 }, // Symbol
      { wch: 10 }, // Type
      { wch: 10 }, // Shares
      { wch: 12 }, // Price
      { wch: 12 }, // Total Value
      { wch: 12 }, // Invalid/Profit
    ];

    // Create worksheet name: "(50.5% -> 30.2%) John Doe"
    const worksheetName = `(${user.originalReturnsRate.toFixed(1)}%->${user.adjustedReturnsRate.toFixed(1)}%) ${user.name}`.substring(0, 31);
    XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
  }

  // Save workbook
  const filename = `suspicious_transactions_${new Date().toISOString().split('T')[0]}.xlsx`;
  const filePath = path.join(process.cwd(), 'exports', filename);
  
  // Create exports directory if it doesn't exist
  const exportsDir = path.join(process.cwd(), 'exports');
  if (!fs.existsSync(exportsDir)) {
    fs.mkdirSync(exportsDir, { recursive: true });
  }

  XLSX.writeFile(workbook, filePath);
  return filePath;
}

async function main() {
  try {
    console.log('Finding suspicious users and their transactions...');
    const suspiciousUsers = await getSuspiciousUsers();

    console.log(`Found ${suspiciousUsers.length} users with >45% returns`);
    console.log('\nInvalid transaction summary:');
    suspiciousUsers.forEach(user => {
      if (user.invalidTransactions.length > 0) {
        console.log(`\n${user.name}:`);
        console.log(`- Original returns: ${user.originalReturnsRate.toFixed(1)}%`);
        console.log(`- Invalid profit: $${user.totalInvalidProfit.toFixed(2)}`);
        console.log(`- Adjusted returns: ${user.adjustedReturnsRate.toFixed(1)}%`);
        console.log(`- Invalid transaction pairs: ${user.invalidTransactions.length}`);
      }
    });
    
    const excelFile = await exportToExcel(suspiciousUsers);
    console.log(`\nResults exported successfully to: ${excelFile}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch((e) => {
  console.error(e);
  prisma.$disconnect();
  exit(1);
});