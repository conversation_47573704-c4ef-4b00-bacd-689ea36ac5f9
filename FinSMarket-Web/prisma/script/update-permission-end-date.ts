import { PrismaClient, Permission } from "@prisma/client";
import { exit } from "process";

const prisma = new PrismaClient();

async function updatePermissionEndDate() {
    try {
        const result = await prisma.userPermission.updateMany({
            where: {
                permission: Permission.free_report_2024,
                deleteAt: null, // Only update active permissions
            },
            data: {
                endAt: null,
            },
        });

        console.log(`Successfully updated ${result.count} permission records to have no end date`);
    } catch (error) {
        console.error(
            `Failed to update permissions: ${error instanceof Error ? error.message : JSON.stringify(error)}`
        );
        throw error;
    }
}

async function main() {
    try {
        await updatePermissionEndDate();
    } catch (error) {
        console.error(error);
        exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

main().catch((e) => {
    console.error(e);
    prisma.$disconnect();
    exit(1);
}); 