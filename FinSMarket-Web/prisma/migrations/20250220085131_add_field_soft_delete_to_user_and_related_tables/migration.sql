-- CreateEnum
CREATE TYPE "UserCreationSource" AS ENUM ('website', 'mobile');

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "createdFrom" "UserCreationSource" NOT NULL DEFAULT 'website',
ADD COLUMN     "deletedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "UserDowJonesPrediction" ADD COLUMN     "deletedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "UserStockView" ADD COLUMN     "deletedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "UserSurveyResponse" ADD COLUMN     "deletedAt" TIMESTAMP(3);
