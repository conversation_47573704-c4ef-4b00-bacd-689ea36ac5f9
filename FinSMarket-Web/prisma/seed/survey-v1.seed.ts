import { locales } from "@/config/language";
import { Language, Permission, PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const surveyList: {
    language:  typeof locales[number];
    tallyId: string;
}[] = [
    { language: "en", tallyId: "nrEogo" },
    { language: "zh-TW", tallyId: "3qPRVY" },
    { language: "ja", tallyId: "3xZJgv" },
    { language: "ko", tallyId: "3N07EO" },
    { language: "th", tallyId: "meEayl" },
    { language: "vi", tallyId: "m6xZ4Y" },
    { language: "ms", tallyId: "wA8Bae" },
    { language: "pt", tallyId: "3j0X7J" },
]

async function main() {
    const surveyV1 = await prisma.survey.upsert({
        where: { name: "survey-1" },
        update: {
            grantPermission: `${Permission.free_report_2024}`,
            versions: {
                updateMany: surveyList.map(survey => ({
                    where: {
                        language: survey.language.replaceAll("-", "_") as Language,
                    },
                    data: {
                        tallyId: survey.tallyId,
                    }
                }))
            }
        },
        create: {
            name: "survey-1",
            grantPermission: `${Permission.free_report_2024}`,
            versions: {
                createMany: {
                    data: surveyList.map(survey => ({
                        language: survey.language.replaceAll("-", "_") as Language,
                        tallyId: survey.tallyId, 
                    })),
                },
            },
        },
    });
    console.log({ surveyV1 });
}
main()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
