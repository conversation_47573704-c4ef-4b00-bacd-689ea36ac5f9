import { PrismaClient, Permission } from "@prisma/client";
import { Argon2id } from "oslo/password";

const prisma = new PrismaClient();

type TestUser = {
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  password: string;
  permissions?: Permission[];
}

async function main() {
    const testUsers: TestUser[] = [
        {
            email: "<EMAIL>",
            firstName: "Test",
            lastName: "User",
            name: "Test User",
            password: "a1B2c3D4e5F6g7H8",
            permissions: [Permission.free_report_2024],
        },
        {
            email: "<EMAIL>",
            firstName: "<PERSON>",
            lastName: "<PERSON>",
            name: "<PERSON>",
            password: "i9J0k1L2m3N4o5P6",
            permissions: [
                Permission.free_report_2024,
                Permission.admin,
            ],
        },
        {
            email: "<EMAIL>",
            firstName: "<PERSON>",
            lastName: "<PERSON>",
            name: "<PERSON>",
            password: "q7R8s9T0u1V2w3X4",
        },
        {
            email: "<EMAIL>",
            firstName: "Apple",
            lastName: "Tester",
            name: "<PERSON> Tester",
            password: "y5Z6a7B8c9D0e1F2",
        },
        {
            email: "<EMAIL>",
            firstName: "Google",
            lastName: "Tester",
            name: "Google Tester",
            password: "g3H4i5J6k7L8m9N0",
        },
    ];

    // Create users with hashed passwords
    for (const user of testUsers) {
        const hashedPassword = await new Argon2id().hash(user.password);
        
        const createdUser = await prisma.user.upsert({
            where: { email: user.email },
            update: {
                firstName: user.firstName,
                lastName: user.lastName,
                name: user.name,
                hashedPassword,
                verifiedAt: new Date(),
            },
            create: {
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                name: user.name,
                hashedPassword,
                verifiedAt: new Date(),
            },
        });

        // Delete existing permissions first
        await prisma.userPermission.deleteMany({
            where: { userId: createdUser.id },
        });

        // Add specified permissions
        if (user.permissions && user.permissions.length > 0) {
            await Promise.all(
                user.permissions.map((permission) =>
                    prisma.userPermission.create({
                        data: {
                            userId: createdUser.id,
                            permission: permission,
                            startAt: new Date(),
                        },
                    })
                )
            );
        }
    }

    console.log('Successfully created test users: ', JSON.stringify(testUsers, null, 2));
}

main()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    }); 