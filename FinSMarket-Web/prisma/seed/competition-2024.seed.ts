import { PrismaClient } from "@prisma/client";
import { fromZonedTime } from 'date-fns-tz';
import { COMPETITION_TIMEZONE } from "@/utils/stock-market";

const prisma = new PrismaClient();

// The competition will start on 2024-11-01 and end on 2024-12-31 in UTC-4
// The bonus will be available until 2024-09-30 in UTC-4
// All times are stored in UTC format, but represent UTC-4 times

async function main() {
    const startDate = fromZonedTime("2024-11-01 09:30", COMPETITION_TIMEZONE);
    const endDate = fromZonedTime("2024-12-31 16:00", COMPETITION_TIMEZONE);
    const bonusEndDate = fromZonedTime("2024-09-30 23:59:59", COMPETITION_TIMEZONE);

    const competition2024 = await prisma.competition.upsert({
        where: { translationKey: "competition-2024" },
        update: {
            startDate,
            endDate,
            baseBalance: 1000000,
            bonusAmount: 250000,
            bonusEndDate,
        },
        create: {
            translationKey: "competition-2024",
            startDate,
            endDate,
            baseBalance: 1000000,
            bonusAmount: 250000,
            bonusEndDate,
        },
    });
    console.log('Successfully created competition: ', { competition2024 });

    // Seed Dow Jones Prediction Events
    const dowJonesEvents = [
        {
            translationKey: 'presidential_election_day',
            name: 'Presidential Election Day',
            eventDate: fromZonedTime("2024-11-05 09:30", COMPETITION_TIMEZONE),
        },
        {
            translationKey: 'q3_gdp_data_release',
            name: '2024 Q3 GDP Data Release Day',
            eventDate: fromZonedTime("2024-11-27 09:30", COMPETITION_TIMEZONE),
        },
        {
            translationKey: 'non_farm_payroll_data_release',
            name: 'Non-Farm Payroll Data Release Day',
            eventDate: fromZonedTime("2024-12-05 09:30", COMPETITION_TIMEZONE),
        },
        {
            translationKey: 'federal_reserve_meeting',
            name: 'Federal Reserve Meeting Day',
            eventDate: fromZonedTime("2024-12-11 09:30", COMPETITION_TIMEZONE),
        },
        {
            translationKey: 'december_options_expiration',
            name: 'December Options Expiration Day',
            eventDate: fromZonedTime("2024-12-20 09:30", COMPETITION_TIMEZONE),
        },
    ];

    const dowJonesEventsCreated = await prisma.$transaction(
        dowJonesEvents.map((event) =>
            prisma.dowJonesPredictionEvent.upsert({
                where: { translationKey: event.translationKey },
                update: {
                    name: event.name,
                    eventDate: event.eventDate,
                },
                create: {
                    translationKey: event.translationKey,
                    name: event.name,
                    eventDate: event.eventDate,
                },
            })
        )
    );

    console.log('Successfully created Dow Jones Prediction Events: ', { dowJonesEventsCreated });
}

main()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
