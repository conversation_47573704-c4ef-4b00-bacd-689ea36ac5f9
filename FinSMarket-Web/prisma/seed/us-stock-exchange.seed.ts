import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  const exchanges = [
    { code: 'AMEX', name: 'American Stock Exchange', country: 'US' },
    { code: 'NASDAQ', name: 'NASDAQ Stock Market', country: 'US' },
    { code: 'NYSE', name: 'New York Stock Exchange', country: 'US' },
  ];

  const exchangesResult = await prisma.$transaction(
    exchanges.map((exchange) =>
      prisma.stockExchange.upsert({
        where: { code: exchange.code },
        update: {
          name: exchange.name,
          country: exchange.country,
        },
        create: exchange,
      })
    )
  );

  console.log('Successfully seeded US stock exchanges: ', { exchangesResult });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
