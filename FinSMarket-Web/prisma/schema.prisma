generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider       = "zod-prisma-types"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Language {
  en
  zh_TW
  ja
  ko
  th
  vi
  ms
  pt
}

enum Permission {
  free_report_2024
  portfolio_manager
  paid_tier_1 // starting from cheapest
  admin // can access admin panel
}

enum TransactionType {
  BUY
  SELL
}

enum UserCreationSource {
  website
  mobile
}

enum AuthProvider {
  EMAIL
  GOOGLE
  APPLE
}

enum ChatMessageRole {
  USER
  AI
}

enum FeedbackType {
  like
  dislike
  report
  suggestion
}

model User {
  id                      String               @id @default(cuid())
  email                   String               @unique
  verifiedAt              DateTime?
  firstName               String?
  lastName                String?
  name                    String?
  hashedPassword          String?
  picture                 String?
  createdAt               DateTime             @default(now())
  createdFrom             UserCreationSource   @default(website)
  deletedAt               DateTime?

  session                 Session[]
  permissions             UserPermission[]
  stockViews              UserStockView[]
  responses               UserSurveyResponse[]
  emailVerificationTokens EmailVerificationToken[]
  portfolios              Portfolio[]
  dowJonesPredictions     UserDowJonesPrediction[]
  passwordResetTokens     PasswordResetToken[]
  authProviders           UserAuthProvider[]
  chats                   Chat[]
  chatFeedbacks           ChatFeedback[]
  appFeedbacks            UserFeedback[]
}

model UserPermission {
  id  Int @id @default(autoincrement())
  userId  String
  permission Permission
  startAt DateTime?
  endAt DateTime?
  deleteAt DateTime?

  user  User  @relation(fields: [userId], references: [id])
}

model Session {
  id        String   @id
  userId    String
  expiresAt DateTime

  user      User     @relation(fields: [userId], references: [id])
}

model EmailVerificationToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime

  user      User     @relation(fields: [userId], references: [id])
}

model PasswordResetToken {
    id        Int      @id @default(autoincrement())
    token     String   @unique
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    createdAt DateTime @default(now())
    expiresAt DateTime
    
    @@index([userId])
    @@index([token])
}

// Stocks
enum StockType {
  STOCK
  ETF
  TRUST
  OTHER
}

model StockExchange {
  id        Int      @id @default(autoincrement())
  code      String   @unique
  name      String
  country   String?
  // timezone  String?
  stocks    Stock[]
}

model Stock {
  id              Int      @id @default(autoincrement())
  symbol          String   @unique
  type            StockType
  name            String
  exchangeId      Int

  exchange        StockExchange @relation(fields: [exchangeId], references: [id])
  prices          StockPrice[]

  @@index([symbol])
  @@index([name])
}

model StockPrice {
  id              Int      @id @default(autoincrement())
  stockId         Int
  price           Float
  isClosingPrice  Boolean
  timestamp       DateTime @default(now())

  stock           Stock    @relation(fields: [stockId], references: [id])

  @@index([stockId, isClosingPrice, timestamp(sort: Desc)])
}

model UserStockView {
  id        Int      @id @default(autoincrement())
  userId    String
  symbol    String
  viewedAt  DateTime @default(now())
  deletedAt DateTime?
  
  user      User     @relation(fields: [userId], references: [id])
  
  // @@unique([userId, symbol])
}

model Competition {
  id                Int        @id @default(autoincrement())
  translationKey    String     @unique
  startDate         DateTime
  endDate           DateTime
  baseBalance       Float
  bonusAmount       Float      @default(0)
  bonusEndDate      DateTime?
  lastCalculatedAt DateTime?
  
  entries        CompetitionEntry[]
}

model CompetitionEntry {
  id                Int      @id @default(autoincrement())
  portfolioId       Int      @unique
  competitionId     Int
  rank              Int?
  latestUpdatedAt   DateTime @updatedAt
  createdAt         DateTime @default(now())

  portfolio         Portfolio   @relation(fields: [portfolioId], references: [id])
  competition       Competition @relation(fields: [competitionId], references: [id])
}

model Portfolio {
  id                Int      @id @default(autoincrement())
  userId            String
  name              String?
  initialBalance    Float    @default(0)
  cashBalance       Float    @default(0)
  investedValue     Float    @default(0)
  realizedGain      Float    @default(0)
  unrealizedGain    Float    @default(0)
  latestUpdatedAt   DateTime @updatedAt
  createdAt         DateTime @default(now())
  deletedAt         DateTime?

  user              User     @relation(fields: [userId], references: [id])
  positions         Position[]
  transactions      Transaction[]
  competitionEntry  CompetitionEntry?
  snapshots         PortfolioSnapshot[]
  rankings          PortfolioRanking[]
}

model PortfolioSnapshot {
  id             Int      @id @default(autoincrement())
  portfolioId    Int
  date           DateTime @default(now())
  totalValue     Float    // cashBalance + investedValue + unrealizedGain
  cashBalance    Float    // cashBalance
  investedValue  Float    // total amount of money invested in stocks at their purchase prices,
  realizedGain   Float    // total realized gain or loss from selling stocks
  unrealizedGain Float    // total unrealized gain or loss from holding stocks
  returns        Float    // totalValue - investedValue
  returnsRate    Float    // return / investedValue
  positionData   Json    // Stores position prices and quantities

  portfolio      Portfolio @relation(fields: [portfolioId], references: [id])

  @@index([portfolioId, date])
}

model PortfolioRanking {
  id          Int      @id @default(autoincrement())
  portfolioId Int
  rank        Int
  returnRate  Float
  returns     Float @default(-1)
  interval    RankingInterval
  date        DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  portfolio   Portfolio @relation(fields: [portfolioId], references: [id])

  @@unique([portfolioId, interval, date])
  @@index([interval, date, rank])
}

enum RankingInterval {
  DAILY
  WEEKLY
  MONTHLY
}

model Position {
  id            Int      @id @default(autoincrement())
  portfolioId   Int
  symbol        String
  shares        Int
  averagePrice  Float
  latestPrice   Float    @default(0)
  latestUpdatedAt DateTime @updatedAt
  createdAt     DateTime @default(now())

  portfolio    Portfolio @relation(fields: [portfolioId], references: [id])
  transactions Transaction[]
 
  @@unique([portfolioId, symbol])
}

model Transaction {
  id           Int      @id @default(autoincrement())
  portfolioId  Int
  positionId   Int
  symbol       String
  type         TransactionType
  shares       Float
  price        Float
  timestamp    DateTime @default(now())
  
  portfolio    Portfolio @relation(fields: [portfolioId], references: [id])
  position     Position  @relation(fields: [positionId], references: [id])
}

model Survey {
  id        String   @id @default(uuid())
  name      String   @unique
  grantPermission String?
  createAt  DateTime @default(now())

  versions  SurveyVersion[]
  responses UserSurveyResponse[]
}

model SurveyVersion {
  id        Int      @id @default(autoincrement())
  surveyId  String
  language  Language
  tallyId   String

  survey    Survey   @relation(fields: [surveyId], references: [id])
  responses UserSurveyResponse[]
}

model UserSurveyResponse {
  id              String        @id @default(uuid())
  userId          String
  surveyId        String
  surveyVersionId Int
  payload         Json
  completedAt     DateTime      @default(now())
  deletedAt       DateTime?

  user            User          @relation(fields: [userId], references: [id])
  survey          Survey        @relation(fields: [surveyId], references: [id])
  surveyVersion   SurveyVersion @relation(fields: [surveyVersionId], references: [id])
}

model DowJonesPredictionEvent {
  id                        String      @id @default(uuid())
  translationKey            String   @unique
  name                      String
  eventDate                 DateTime
  actualValue               Float?
  isClosestUserCalculated   Boolean  @default(false)

  predictions               UserDowJonesPrediction[]
}

model UserDowJonesPrediction {
  id            Int         @id @default(autoincrement())
  userId        String
  eventId       String
  prediction    Float
  isWinner      Boolean     @default(false)
  createdAt     DateTime    @default(now())
  deletedAt     DateTime?

  user          User        @relation(fields: [userId], references: [id])
  event         DowJonesPredictionEvent @relation(fields: [eventId], references: [id])

  @@unique([userId, eventId])
}

model UserAuthProvider {
  id            String       @id @default(cuid())
  userId        String
  provider      AuthProvider
  providerId    String       // The ID from OAuth provider (Google/Apple ID)
  email         String       // Email from the provider
  createdAt     DateTime     @default(now())
  lastUsedAt    DateTime     @updatedAt

  user          User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, provider])
  @@index([userId])
}

model Chat {
  id          String        @id @default(cuid())
  userId      String
  title       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  deletedAt   DateTime?

  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages    ChatMessage[]

  @@index([userId, createdAt])
}

model ChatMessage {
  id          String          @id @default(cuid())
  chatId      String
  role        ChatMessageRole
  content     String
  parentId    String?         // Links AI responses to the user message
  chart       Json?           // Chart metadata for messages with charts
  createdAt   DateTime        @default(now())
  deletedAt   DateTime?

  chat        Chat            @relation(fields: [chatId], references: [id], onDelete: Cascade)
  // Self-relation to link AI responses to a user message
  parent      ChatMessage?    @relation("Responses", fields: [parentId], references: [id], onDelete: Cascade)
  responses   ChatMessage[]   @relation("Responses")
  // Feedback relation for each AI response
  feedback    ChatFeedback?

  @@index([chatId, createdAt])
  @@index([parentId])
}

model ChatFeedback {
  id          String       @id @default(cuid())
  messageId   String       @unique
  userId      String
  feedback    String       // e.g., "LIKE", "DISLIKE", or detailed text
  createdAt   DateTime     @default(now())

  message     ChatMessage  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([messageId])
}

model UserFeedback {
  id            String       @id @default(cuid())
  userId        String?
  feature       String       // Feature/area identifier (e.g., "ai_response", "ui_dashboard")
  feedbackType  FeedbackType // Enum for feedback sentiment
  reasons       String[]     // Array of reason codes or free-text strings
  comments      String?      // Optional free-text user comments
  rating        Int?         // Optional numeric rating (e.g., 1-5 stars)
  metadata      Json?        // Optional flexible JSON for feature-specific data
  createdAt     DateTime     @default(now())

  user          User?         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([feature])
  @@index([feedbackType])
  @@index([createdAt])
}
