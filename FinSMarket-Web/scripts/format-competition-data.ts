import fs from 'fs';
import path from 'path';

// Read the input JSON file
const inputPath = path.join(process.cwd(), 'tmp', 'whatsapp.json');
const outputPath = path.join(process.cwd(), 'tmp', 'formatted-competition-data.json');

try {
  // Read and parse the input JSON
  const rawData = fs.readFileSync(inputPath, 'utf8');
  const data = JSON.parse(rawData);

  // Transform the data into an array format
  const formattedData = Object.entries(data).map(([rank, details]: [string, any]) => ({
    name: details.name,
    return: details.return,
    rank: parseInt(rank)
  }));

  // Sort by rank (optional, as it should already be sorted)
  formattedData.sort((a, b) => a.rank - b.rank);

  // Write the formatted data to a new file
  fs.writeFileSync(
    outputPath,
    JSON.stringify(formattedData, null, 2),
    'utf8'
  );

  console.log('Data has been successfully formatted and saved to:', outputPath);
} catch (error) {
  console.error('Error processing the file:', error);
}