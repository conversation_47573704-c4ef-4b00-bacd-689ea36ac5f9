import dotenv from 'dotenv';
import path from 'path';
import { SyncService } from '../../services/sync-service';
import UserRepo from '@/repository/user';

dotenv.config({ path: path.resolve(process.cwd(), '.env'), quiet: true });

async function syncAllUsers() {
  console.log('Starting to sync all users...');
  try {
    await SyncService.syncAllUser();
    
    console.log('All users synced successfully!');
  } catch (error) {
    console.error('Error syncing users:', error);
  }
}

syncAllUsers();
