import fs from 'fs';
import path from 'path';

const PROBLEMATIC_PATTERNS = [
  /className={\s*`[^`]*\${.*?}[^`]*`}/g,  // Template literals in className
  /class={\s*`[^`]*\${.*?}[^`]*`}/g,      // Template literals in class
  /className={\s*[^}]*\+\s*[^}]*}/g,      // String concatenation
];

const EXCLUDED_DIRS = ['node_modules', '.next', 'dist', '.git'];

function scanFile(filePath: string): void {
  const content = fs.readFileSync(filePath, 'utf8');
  
  PROBLEMATIC_PATTERNS.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      console.log(`\nPotential issue in ${filePath}:`);
      matches.forEach(match => {
        console.log(`  - ${match}`);
      });
    }
  });
}

function scanDir(dir: string): void {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    
    if (EXCLUDED_DIRS.includes(file)) return;
    
    if (fs.statSync(fullPath).isDirectory()) {
      scanDir(fullPath);
    } else if (/\.(tsx|jsx|js|ts)$/.test(file)) {
      scanFile(fullPath);
    }
  });
}

console.log('Scanning for problematic Tailwind class patterns...');
scanDir('.'); 