import { RankingResult } from "@/prisma/script/export-competition-rankings";
import { FinalLeaderboardRanking } from "@/types/api/competition";
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';

// Add safe import for whatsapp data
let whatsappData: any[] = [];
try {
  whatsappData = require("@/tmp/whatsapp.json");
} catch (error) {
  console.log("Whatsapp data not found, proceeding with website rankings only");
}

// Add safe import for overall rankings
let overallRankings: RankingResult[] = [];
try {
  const { overallRankings: rankings } = require("@/exports/final-rankings");
  overallRankings = rankings;
} catch (error) {
  console.log("Overall rankings data not found, proceeding with empty website rankings");
}

// Convert website data to FinalLeaderboardRanking
const websiteRankings: FinalLeaderboardRanking[] = overallRankings.map((ranking) => ({
  rank: ranking.rank,
  name: ranking.userName || "Unknown",
  platform: "website" as const,
  returns: ranking.finalBalance,
  returnRate: ranking.profitRate,
}));

// Convert whatsapp data to FinalLeaderboardRanking only if data exists
const whatsappRankings: FinalLeaderboardRanking[] = whatsappData.length > 0
  ? whatsappData
    .filter((ranking): ranking is { name: string; return: number; rank: number } => {
      return typeof ranking.name === 'string' && 
             typeof ranking.return === 'number' && 
             typeof ranking.rank === 'number';
    })
    .map((ranking) => {
      const initialBalance = 1000000;
      const returns = initialBalance * (1 + ranking.return / 100);
      
      return {
        rank: ranking.rank,
        name: ranking.name,
        platform: "whatsapp" as const,
        returns: returns,
        returnRate: ranking.return,
      };
    })
  : [];

// Combine and sort all rankings
const allRankings: FinalLeaderboardRanking[] = [...websiteRankings, ...whatsappRankings].sort(
  (a, b) => b.returnRate - a.returnRate
);

// Re-rank based on combined results
const finalRankings: FinalLeaderboardRanking[] = allRankings.map((ranking, index) => ({
  ...ranking,
  rank: index + 1,
}));

// Create the export file content
const fileContent = `import { FinalLeaderboardRanking } from "@/types/api/competition";

export const finalLeaderboardRankings: FinalLeaderboardRanking[] = ${JSON.stringify(finalRankings, null, 2)};
`;

// Write to TypeScript file
fs.writeFileSync(path.join(process.cwd(), 'tmp', 'final-leaderboard.ts'), fileContent);
console.log("Final rankings have been written to tmp/final-leaderboard.ts");

// Export to Excel
function exportToExcel(rankings: FinalLeaderboardRanking[]) {
  const workbook = XLSX.utils.book_new();

  // Format rankings for Excel
  const formattedRankings = rankings.slice(0, 150).map(r => ({
    Rank: r.rank,
    Name: r.name,
    Platform: r.platform,
    'Returns (HKD)': r.returns.toFixed(2),
    'Return Rate (%)': r.returnRate.toFixed(2),
  }));

  // Define column widths
  const columnWidths = [
    { wch: 6 },    // Rank
    { wch: 25 },   // Name
    { wch: 12 },   // Platform
    { wch: 15 },   // Returns
    { wch: 15 },   // Return Rate
  ];

  // Create worksheet
  const worksheet = XLSX.utils.json_to_sheet(formattedRankings);
  worksheet['!cols'] = columnWidths;

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Final Rankings");

  // Save workbook
  const filename = `final_rankings_${new Date().toISOString().split('T')[0]}.xlsx`;
  const filePath = path.join(process.cwd(), 'exports', filename);
  XLSX.writeFile(workbook, filePath);
  
  return filePath;
}

// Export to Excel
const excelFile = exportToExcel(finalRankings);
console.log(`Final rankings exported to Excel: ${excelFile}`);
