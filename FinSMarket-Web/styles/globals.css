@tailwind base;
@tailwind components;
@tailwind utilities;

.frosted-noise::before {
  content: "";
  position: absolute;
  top: 0;
  left:0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-image: url(/assets/noise.png);
  background-repeat: repeat;
  background-size: 100px;
  opacity: .03;
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

.highlighted-text {
  position: relative;
  z-index: 1;
}

.highlighted-text::after { 
  content: "";
  z-index: -1;
  position: absolute;
  height: 10%;
  width: 100%;
  left: 0;
  bottom: 0;
  transform: translateY(80%);
  background: linear-gradient(90deg, rgba(255,191,139,1) 0%, rgba(251,127,23,1) 100%);
  /* background-color: #FF9339; */
  opacity: 70%;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Original chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --chart-6: 266 54% 61%;
    --chart-7: 335 80% 65%;
    --chart-8: 16 85% 74%;
    --chart-9: 199 84% 55%;
    --chart-10: 130 43% 57%;

    /* Reordered and randomized stock colors */
    --main: 14 100% 72%;
    --stock-1: 195 45% 46%;
    --stock-2: 67.89 50.67% 70.59%;
    --stock-3: 46 76% 76%; 
    --stock-4: 207 50% 85%;
    --stock-5: 31 58% 72%; 
    --stock-6: 345 68% 69%;
    --stock-7: 182 53% 84%;
    --stock-8: 320 12% 41%;
    --stock-9: 228 33% 85%;
    --stock-10: 7 98% 70%; 
    --stock-11: 157 74% 84%
    --stock-12: 93 16% 72%;
    --stock-13: 184 7% 61%;
    --stock-14: 198 71% 34%;
    --stock-15: 322 50% 87%;
    --stock-16: 347 33% 89%
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Original chart colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --chart-6: 190 70% 50%;
    --chart-7: 60 80% 60%;
    --chart-8: 310 60% 55%;
    --chart-9: 100 65% 45%;
    --chart-10: 250 55% 60%;

    /* Reordered and randomized stock colors */
    --main: 14 100% 72%;
    --stock-1: 195 45% 46%;
    --stock-2: 67.89 50.67% 70.59%;
    --stock-3: 46 76% 76%; 
    --stock-4: 67.89 50.67% 70.59;
    --stock-5: 31 58% 72%; 
    --stock-6: 345 68% 69%;
    --stock-7: 182 53% 84%;
    --stock-8: 320 12% 41%;
    --stock-9: 228 33% 85%;
    --stock-10: 7 98% 70%; 
    --stock-11: 157 74% 84%
    --stock-12: 93 16% 72%;
    --stock-13: 184 7% 61%;
    --stock-14: 207 50% 85%
    --stock-15: 322 50% 87%;
    --stock-16: 347 33% 89%
    --stock-17: 198 71% 34%
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
 /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}
