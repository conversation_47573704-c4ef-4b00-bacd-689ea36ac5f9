/* Sign-in button text */
"Sign in" = "היכנס";

/* Long form sign-in button text */
"Sign in with Google" = "היכנס באמצעות Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "אישור";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "ביטול";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "הגדרות";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "לא ניתן להיכנס לחשבון";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "מנהל המערכת דורש ממך להגדיר קוד סיסמה במכשיר זה כדי לגשת לחשבון זה. יש להגדיר קוד סיסמה ולנסות שוב.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "המכשיר אינו פועל בהתאם למדיניות האבטחה שנקבעה על-ידי מנהל המערכת.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "האם להתחבר באמצעות האפליקציית Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "על מנת להגן על נתוני הארגון שלך, יש להתחבר באמצעות אפליקציית Device Policy לפני הכניסה לחשבון.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "התחברות";
