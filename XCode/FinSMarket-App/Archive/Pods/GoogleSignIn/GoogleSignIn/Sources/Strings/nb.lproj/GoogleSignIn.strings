/* Sign-in button text */
"Sign in" = "Logg på";

/* Long form sign-in button text */
"Sign in with Google" = "Logg på med Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Avbryt";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Innstillinger";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Kan ikke logge på kontoen";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Administratoren din krever at du angir en adgangskode på denne enheten for å få tilgang til kontoen. Angi en adgangskode, og prøv på nytt.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Enheten overholder ikke retning<PERSON>lin<PERSON> for sikkerhet som ble angitt av administratoren din.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Vil du koble til med Device Policy-appen?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "For å beskytte dataene til organisasjonen din må du koble til med Device Policy-appen før du logger på.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Koble til";
