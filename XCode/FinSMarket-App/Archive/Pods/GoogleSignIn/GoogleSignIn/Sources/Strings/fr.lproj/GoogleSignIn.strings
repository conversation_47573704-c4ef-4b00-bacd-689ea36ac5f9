/* Sign-in button text */
"Sign in" = "Se connecter";

/* Long form sign-in button text */
"Sign in with Google" = "Se connecter avec Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Annuler";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Paramètres";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Impossible de se connecter au compte";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Votre administrateur exige que vous définissiez un mot de passe sur cet appareil pour accéder à ce compte. Veuillez définir un mot de passe, puis réessayer.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "L'appareil ne respecte pas les règles de sécurité définies par votre administrateur.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Se connecter à l'application Device Policy ?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Afin de protéger les données de votre organisation, vous devez vous connecter à l'application Device Policy avant de vous connecter à votre compte.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Connexion";
