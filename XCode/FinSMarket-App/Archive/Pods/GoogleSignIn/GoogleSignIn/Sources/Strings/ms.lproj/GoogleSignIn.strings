/* Sign-in button text */
"Sign in" = "Log masuk";

/* Long form sign-in button text */
"Sign in with Google" = "Log masuk dengan Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Batal";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Tetapan";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Tidak dapat log masuk ke akaun";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Pentadbir menghendaki anda menetapkan kod laluan pada peranti ini untuk mengakses akaun ini. Sila tetapkan kod laluan, kemudian cuba lagi.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Peranti tidak mematuhi dasar keselamatan yang ditetapkan oleh pentadbir anda.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Berhubung dengan Apl Dasar Peranti?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Untuk melindungi data organisasi anda, anda mesti berhubung dengan apl Dasar Peranti sebelum log masuk.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Hubungkan";
