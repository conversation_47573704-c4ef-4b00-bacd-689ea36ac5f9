{"name": "React-timing", "version": "0.76.9", "summary": "React Native timing primitives", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.76.9"}, "source_files": "*.{cpp,h}", "header_dir": "react/timing", "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "", "DEFINES_MODULE": "YES"}}