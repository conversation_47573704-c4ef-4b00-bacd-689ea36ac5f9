{"name": "react-native-keyboard-controller", "version": "1.18.2", "summary": "Keyboard manager which works in identical way on both iOS and Android", "homepage": "https://kirillzyusko.github.io/react-native-keyboard-controller/", "license": "MIT", "authors": "<PERSON><PERSON> <<EMAIL>> (https://github.com/kirillzyusko)", "platforms": {"ios": "11.0"}, "source": {"git": "https://github.com/kirillzyusko/react-native-keyboard-controller.git", "tag": "1.18.2"}, "source_files": "ios/**/*.{h,m,mm,swift}", "exclude_files": "ios/KeyboardControllerNative/**/*", "public_header_files": "ios/**/*.h", "pod_target_xcconfig": {"OTHER_SWIFT_FLAGS": "-DKEYBOARD_CONTROLLER_NEW_ARCH_ENABLED", "HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.10.14.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "hermes-engine": []}, "subspecs": [{"name": "common", "source_files": ["common/cpp/**/*.{cpp,h}"], "header_dir": "react/renderer/components/reactnativekeyboardcontroller", "private_header_files": "common/cpp/**/*.{h}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/common/cpp\""}}]}