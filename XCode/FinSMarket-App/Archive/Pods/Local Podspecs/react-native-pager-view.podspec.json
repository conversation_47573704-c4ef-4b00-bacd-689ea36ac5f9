{"name": "react-native-pager-view", "version": "6.5.1", "summary": "React Native wrapper for Android and iOS ViewPager", "homepage": "https://github.com/callstack/react-native-pager-view#readme", "license": "MIT", "authors": "tro<PERSON><PERSON> <<EMAIL>> (https://github.com/callstack)", "platforms": {"ios": "10.0", "visionos": "1.0"}, "source": {"git": "https://github.com/callstack/react-native-pager-view.git", "tag": "6.5.1"}, "source_files": "ios/**/*.{h,m,mm}", "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.10.14.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "hermes-engine": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "subspecs": [{"name": "common", "source_files": "common/cpp/**/*.{cpp,h}", "header_dir": "RNCViewPager", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/common/cpp\""}}]}