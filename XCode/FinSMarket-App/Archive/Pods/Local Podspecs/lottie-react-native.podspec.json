{"name": "lottie-react-native", "version": "7.3.1", "summary": "React Native bindings for <PERSON><PERSON>", "license": "Apache-2.0", "authors": "<PERSON> <emili<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "homepage": "https://airbnb.io/lottie/#/react-native", "platforms": {"ios": "13.4", "osx": "10.15", "tvos": "13.0", "visionos": "1.0"}, "source": {"git": "https://github.com/lottie-react-native/lottie-react-native.git", "tag": "v7.3.1"}, "source_files": "ios/**/*.{h,m,mm,swift}", "resource_bundles": {"Lottie_React_Native_Privacy": ["ios/PrivacyInfo.xcprivacy"]}, "dependencies": {"lottie-ios": ["4.5.0"], "React-Core": [], "RCT-Folly": ["2024.10.14.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "hermes-engine": []}, "swift_versions": "5.9", "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "swift_version": "5.9"}