{"name": "fast_float", "version": "6.1.4", "license": {"type": "MIT"}, "homepage": "https://github.com/fastfloat/fast_float", "summary": "{fast_float} is an open-source number parsing library for C++. The library provides fast header-only implementations.", "authors": "The fast_float contributors", "source": {"git": "https://github.com/fastfloat/fast_float.git", "tag": "v6.1.4"}, "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES"}, "platforms": {"ios": "15.1"}, "libraries": "c++", "public_header_files": "include/fast_float/*.h", "header_mappings_dir": "include", "source_files": ["include/fast_float/*.h"]}