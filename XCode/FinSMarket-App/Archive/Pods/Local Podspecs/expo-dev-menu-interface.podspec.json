{"name": "expo-dev-menu-interface", "version": "1.9.3", "summary": "Interface for expo-dev-menu", "description": "Interface for expo-dev-menu", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev", "platforms": {"ios": "15.1"}, "swift_versions": "5.2", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "source_files": "**/*.{h,m,swift}", "preserve_paths": "**/*.{h,m,swift}", "requires_arc": true, "header_dir": "EXDevMenuInterface", "user_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu-interface/Swift Compatibility Header\""}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_version": "5.2"}